import { config } from '@tamagui/config/v3';
import { createTamagui } from '@tamagui/core';

// 自定义主题配置
const customConfig = {
  ...config,
  themes: {
    ...config.themes,
    // 自定义主题
    light: {
      ...config.themes.light,
      primary: '#007AFF',
      primaryHover: '#0056CC',
      secondary: '#5856D6',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      background: '#FFFFFF',
      backgroundHover: '#F2F2F7',
      backgroundPress: '#E5E5EA',
      backgroundFocus: '#F2F2F7',
      borderColor: '#C6C6C8',
      borderColorHover: '#A8A8AA',
      color: '#000000',
      colorHover: '#1C1C1E',
      placeholderColor: '#8E8E93',
    },
    dark: {
      ...config.themes.dark,
      primary: '#0A84FF',
      primaryHover: '#409CFF',
      secondary: '#5E5CE6',
      success: '#30D158',
      warning: '#FF9F0A',
      error: '#FF453A',
      background: '#000000',
      backgroundHover: '#1C1C1E',
      backgroundPress: '#2C2C2E',
      backgroundFocus: '#1C1C1E',
      borderColor: '#38383A',
      borderColorHover: '#48484A',
      color: '#FFFFFF',
      colorHover: '#F2F2F7',
      placeholderColor: '#8E8E93',
    },
  },
};

const appConfig = createTamagui(customConfig);

export default appConfig;

export type Conf = typeof appConfig;

declare module '@tamagui/core' {
  interface TamaguiCustomConfig extends Conf {}
}
