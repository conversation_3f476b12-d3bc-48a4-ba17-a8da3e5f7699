/**
 * 表单验证组件和表单管理器
 * 提供统一的表单验证和状态管理
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { YStack, styled } from 'tamagui';

// 字段验证状态
export interface FieldValidation {
  isValid: boolean;
  errorMessage?: string;
  touched: boolean;
}

// 字段注册信息
export interface FieldRegistration {
  validate: () => void;
  getValue: () => any;
  setValue: (value: any) => void;
}

// 表单状态
export interface FormState {
  fields: Record<string, FieldValidation>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
}

// 表单上下文类型
export interface FormContextType {
  formState: FormState;
  registerField: (name: string, registration: FieldRegistration) => void;
  unregisterField: (name: string) => void;
  setFieldValidation: (name: string, isValid: boolean, errorMessage?: string) => void;
  setFieldTouched: (name: string, touched: boolean) => void;
  validateField: (name: string) => boolean;
  validateForm: () => boolean;
  resetForm: () => void;
  submitForm: () => Promise<boolean>;
  getFieldValue: (name: string) => any;
  setFieldValue: (name: string, value: any) => void;
  getFormValues: () => Record<string, any>;
  setFormValues: (values: Record<string, any>) => void;
}

// 表单提供者属性
export interface FormProviderProps {
  children: ReactNode;
  onSubmit?: (values: Record<string, any>) => Promise<void> | void;
  initialValues?: Record<string, any>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

// 表单组件属性
export interface FormProps extends FormProviderProps {
  style?: any;
}

// 样式化表单容器
const FormContainer = styled(YStack, {
  name: 'FormContainer',
  gap: '$4',
});

// 创建表单上下文
const FormContext = createContext<FormContextType | null>(null);

// 表单提供者组件
export const FormProvider: React.FC<FormProviderProps> = ({
  children,
  onSubmit,
  initialValues = {},
  validateOnChange = true,
  validateOnBlur = true,
}) => {
  // 表单状态
  const [formState, setFormState] = useState<FormState>({
    fields: {},
    isValid: true,
    isSubmitting: false,
    submitCount: 0,
  });

  // 字段注册信息
  const [fieldRegistrations, setFieldRegistrations] = useState<Record<string, FieldRegistration>>({});

  // 注册字段
  const registerField = useCallback((name: string, registration: FieldRegistration) => {
    setFieldRegistrations(prev => ({
      ...prev,
      [name]: registration,
    }));

    // 初始化字段状态
    setFormState(prev => ({
      ...prev,
      fields: {
        ...prev.fields,
        [name]: {
          isValid: true,
          errorMessage: undefined,
          touched: false,
        },
      },
    }));

    // 设置初始值
    if (initialValues[name] !== undefined) {
      registration.setValue(initialValues[name]);
    }
  }, [initialValues]);

  // 注销字段
  const unregisterField = useCallback((name: string) => {
    setFieldRegistrations(prev => {
      const newRegistrations = { ...prev };
      delete newRegistrations[name];
      return newRegistrations;
    });

    setFormState(prev => {
      const newFields = { ...prev.fields };
      delete newFields[name];
      return {
        ...prev,
        fields: newFields,
      };
    });
  }, []);

  // 设置字段验证状态
  const setFieldValidation = useCallback((name: string, isValid: boolean, errorMessage?: string) => {
    setFormState(prev => {
      const newFields = {
        ...prev.fields,
        [name]: {
          ...prev.fields[name],
          isValid,
          errorMessage,
        },
      };

      // 计算整体表单是否有效
      const formIsValid = Object.values(newFields).every(field => field.isValid);

      return {
        ...prev,
        fields: newFields,
        isValid: formIsValid,
      };
    });
  }, []);

  // 设置字段触摸状态
  const setFieldTouched = useCallback((name: string, touched: boolean) => {
    setFormState(prev => ({
      ...prev,
      fields: {
        ...prev.fields,
        [name]: {
          ...prev.fields[name],
          touched,
        },
      },
    }));
  }, []);

  // 验证单个字段
  const validateField = useCallback((name: string): boolean => {
    const registration = fieldRegistrations[name];
    if (registration) {
      registration.validate();
      return formState.fields[name]?.isValid || false;
    }
    return true;
  }, [fieldRegistrations, formState.fields]);

  // 验证整个表单
  const validateForm = useCallback((): boolean => {
    let isValid = true;

    Object.keys(fieldRegistrations).forEach(name => {
      const registration = fieldRegistrations[name];
      if (registration) {
        registration.validate();
        if (!formState.fields[name]?.isValid) {
          isValid = false;
        }
      }
    });

    return isValid;
  }, [fieldRegistrations, formState.fields]);

  // 重置表单
  const resetForm = useCallback(() => {
    setFormState({
      fields: {},
      isValid: true,
      isSubmitting: false,
      submitCount: 0,
    });

    // 重置所有字段值
    Object.keys(fieldRegistrations).forEach(name => {
      const registration = fieldRegistrations[name];
      if (registration) {
        registration.setValue(initialValues[name] || '');
      }
    });
  }, [fieldRegistrations, initialValues]);

  // 提交表单
  const submitForm = useCallback(async (): Promise<boolean> => {
    setFormState(prev => ({
      ...prev,
      isSubmitting: true,
      submitCount: prev.submitCount + 1,
    }));

    try {
      // 验证所有字段
      const isValid = validateForm();

      if (!isValid) {
        // 标记所有字段为已触摸，显示错误信息
        Object.keys(fieldRegistrations).forEach(name => {
          setFieldTouched(name, true);
        });

        setFormState(prev => ({
          ...prev,
          isSubmitting: false,
        }));

        return false;
      }

      // 如果有提交处理函数，执行它
      if (onSubmit) {
        const values = getFormValues();
        await onSubmit(values);
      }

      setFormState(prev => ({
        ...prev,
        isSubmitting: false,
      }));

      return true;
    } catch (error) {
      console.error('表单提交失败:', error);
      
      setFormState(prev => ({
        ...prev,
        isSubmitting: false,
      }));

      return false;
    }
  }, [validateForm, fieldRegistrations, onSubmit]);

  // 获取字段值
  const getFieldValue = useCallback((name: string): any => {
    const registration = fieldRegistrations[name];
    return registration ? registration.getValue() : undefined;
  }, [fieldRegistrations]);

  // 设置字段值
  const setFieldValue = useCallback((name: string, value: any) => {
    const registration = fieldRegistrations[name];
    if (registration) {
      registration.setValue(value);
    }
  }, [fieldRegistrations]);

  // 获取所有表单值
  const getFormValues = useCallback((): Record<string, any> => {
    const values: Record<string, any> = {};
    
    Object.keys(fieldRegistrations).forEach(name => {
      const registration = fieldRegistrations[name];
      if (registration) {
        values[name] = registration.getValue();
      }
    });

    return values;
  }, [fieldRegistrations]);

  // 设置所有表单值
  const setFormValues = useCallback((values: Record<string, any>) => {
    Object.keys(values).forEach(name => {
      const registration = fieldRegistrations[name];
      if (registration) {
        registration.setValue(values[name]);
      }
    });
  }, [fieldRegistrations]);

  const contextValue: FormContextType = {
    formState,
    registerField,
    unregisterField,
    setFieldValidation,
    setFieldTouched,
    validateField,
    validateForm,
    resetForm,
    submitForm,
    getFieldValue,
    setFieldValue,
    getFormValues,
    setFormValues,
  };

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  );
};

// 表单组件
export const Form: React.FC<FormProps> = ({
  children,
  style,
  ...providerProps
}) => {
  return (
    <FormProvider {...providerProps}>
      <FormContainer style={style}>
        {children}
      </FormContainer>
    </FormProvider>
  );
};

// 使用表单上下文的Hook
export const useForm = (): FormContextType => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useForm must be used within a FormProvider');
  }
  return context;
};

// 使用字段的Hook
export const useField = (name: string) => {
  const form = useForm();
  const fieldState = form.formState.fields[name];

  return {
    value: form.getFieldValue(name),
    setValue: (value: any) => form.setFieldValue(name, value),
    isValid: fieldState?.isValid ?? true,
    errorMessage: fieldState?.errorMessage,
    touched: fieldState?.touched ?? false,
    setTouched: (touched: boolean) => form.setFieldTouched(name, touched),
    validate: () => form.validateField(name),
  };
};

// 表单验证规则工具函数
export const createValidationSchema = (rules: Record<string, any>) => {
  return rules;
};

// 表单提交按钮组件
export interface FormSubmitButtonProps {
  children: ReactNode;
  onPress?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

export const FormSubmitButton: React.FC<FormSubmitButtonProps> = ({
  children,
  onPress,
  disabled,
  loading,
}) => {
  const form = useForm();

  const handlePress = async () => {
    const success = await form.submitForm();
    if (success) {
      onPress?.();
    }
  };

  return (
    <button
      onClick={handlePress}
      disabled={disabled || form.formState.isSubmitting || loading}
      style={{
        opacity: disabled || form.formState.isSubmitting || loading ? 0.5 : 1,
        cursor: disabled || form.formState.isSubmitting || loading ? 'not-allowed' : 'pointer',
      }}
    >
      {children}
    </button>
  );
};
