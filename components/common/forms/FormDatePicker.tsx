/**
 * 表单日期选择器组件
 * 基于CustomModal实现的日期选择器
 */

import { CustomButton } from '@/components/common/buttons';
import { CustomModal } from '@/components/common/modals';
import React, { useEffect, useState } from 'react';
import { ScrollView, Text, XStack, YStack, styled } from 'tamagui';
import { ValidationRule } from './FormInput';

// 日期选择器组件属性
export interface FormDatePickerProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  value?: Date;
  defaultValue?: Date;
  onDateChange?: (date: Date | null) => void;
  mode?: 'date' | 'time' | 'datetime';
  format?: string;
  minDate?: Date;
  maxDate?: Date;
  rules?: ValidationRule[];
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  showErrorMessage?: boolean;
  onValidationChange?: (name: string, isValid: boolean, errorMessage?: string) => void;
  form?: any;
  size?: 'small' | 'medium' | 'large';
}

// 样式化日期选择器容器
const DatePickerContainer = styled(YStack, {
  name: 'DatePickerContainer',
  gap: '$2',
});

const DatePickerLabel = styled(Text, {
  name: 'DatePickerLabel',
  fontSize: '$4',
  fontWeight: '500',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const DatePickerTrigger = styled(XStack, {
  name: 'DatePickerTrigger',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$3',
  backgroundColor: '$background',
  cursor: 'pointer',
  
  variants: {
    size: {
      small: {
        paddingHorizontal: '$3',
        paddingVertical: '$2',
        minHeight: 36,
      },
      
      medium: {
        paddingHorizontal: '$4',
        paddingVertical: '$3',
        minHeight: 44,
      },
      
      large: {
        paddingHorizontal: '$5',
        paddingVertical: '$4',
        minHeight: 52,
      },
    },
    
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
        backgroundColor: '$backgroundHover',
      },
    },
    
    error: {
      true: {
        borderColor: '$error',
      },
    },
    
    focused: {
      true: {
        borderColor: '$primary',
        shadowColor: '$primary',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
  
  hoverStyle: {
    borderColor: '$borderColorHover',
  },
  
  pressStyle: {
    backgroundColor: '$backgroundPress',
  },
});

const DatePickerValue = styled(Text, {
  name: 'DatePickerValue',
  flex: 1,
  color: '$color',
  
  variants: {
    placeholder: {
      true: {
        color: '$placeholderColor',
      },
    },
    
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const DatePickerIcon = styled(Text, {
  name: 'DatePickerIcon',
  color: '$placeholderColor',
  marginLeft: '$2',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const DatePickerContent = styled(YStack, {
  name: 'DatePickerContent',
  gap: '$4',
  padding: '$4',
});

const DatePickerRow = styled(XStack, {
  name: 'DatePickerRow',
  gap: '$3',
  alignItems: 'center',
});

const PickerColumn = styled(YStack, {
  name: 'PickerColumn',
  flex: 1,
  alignItems: 'center',
  gap: '$2',
});

const PickerTitle = styled(Text, {
  name: 'PickerTitle',
  fontSize: '$4',
  fontWeight: '600',
  color: '$color',
  textAlign: 'center',
});

const PickerScroll = styled(ScrollView, {
  name: 'PickerScroll',
  height: 150,
  width: '100%',
});

const PickerItem = styled(Text, {
  name: 'PickerItem',
  fontSize: '$4',
  color: '$color',
  textAlign: 'center',
  paddingVertical: '$2',
  paddingHorizontal: '$3',
  borderRadius: '$2',
  cursor: 'pointer',
  
  variants: {
    selected: {
      true: {
        backgroundColor: '$primary',
        color: '$white',
      },
    },
  } as const,
  
  hoverStyle: {
    backgroundColor: '$backgroundHover',
  },
});

const ErrorMessage = styled(Text, {
  name: 'ErrorMessage',
  fontSize: '$3',
  color: '$error',
  marginTop: '$1',
  lineHeight: '$3',
});

const RequiredMark = styled(Text, {
  name: 'RequiredMark',
  color: '$error',
  marginLeft: '$1',
});

const ButtonContainer = styled(XStack, {
  name: 'ButtonContainer',
  gap: '$3',
  marginTop: '$4',
});

// 日期格式化函数
const formatDate = (date: Date, format: string, mode: string): string => {
  if (!date) return '';
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  if (format) {
    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes);
  }
  
  switch (mode) {
    case 'date':
      return `${year}-${month}-${day}`;
    case 'time':
      return `${hours}:${minutes}`;
    case 'datetime':
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    default:
      return `${year}-${month}-${day}`;
  }
};

// 生成数字数组
const generateNumbers = (start: number, end: number): number[] => {
  const numbers = [];
  for (let i = start; i <= end; i++) {
    numbers.push(i);
  }
  return numbers;
};

export const FormDatePicker: React.FC<FormDatePickerProps> = ({
  name,
  label,
  placeholder = '请选择日期',
  required = false,
  disabled = false,
  value: controlledValue,
  defaultValue,
  onDateChange,
  mode = 'date',
  format,
  minDate,
  maxDate,
  rules = [],
  validateTrigger = 'onChange',
  showErrorMessage = true,
  onValidationChange,
  form,
  size = 'medium',
}) => {
  const [internalValue, setInternalValue] = useState<Date | null>(
    controlledValue || defaultValue || null
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isValid, setIsValid] = useState(true);
  const [touched, setTouched] = useState(false);
  const [focused, setFocused] = useState(false);
  
  // 临时选择的日期值
  const [tempYear, setTempYear] = useState(new Date().getFullYear());
  const [tempMonth, setTempMonth] = useState(new Date().getMonth() + 1);
  const [tempDay, setTempDay] = useState(new Date().getDate());
  const [tempHour, setTempHour] = useState(new Date().getHours());
  const [tempMinute, setTempMinute] = useState(new Date().getMinutes());

  const currentValue = controlledValue !== undefined ? controlledValue : internalValue;

  // 验证函数
  const validateValue = (value: Date | null): { isValid: boolean; errorMessage: string } => {
    // 合并必填规则
    const allRules = required ? [{ required: true, message: '请选择日期' }, ...rules] : rules;
    
    for (const rule of allRules) {
      // 必填验证
      if (rule.required && !value) {
        return {
          isValid: false,
          errorMessage: rule.message || '请选择日期',
        };
      }

      // 自定义验证
      if (rule.validator && value) {
        const stringValue = formatDate(value, format || '', mode);
        const result = rule.validator(stringValue);
        if (result !== true) {
          return {
            isValid: false,
            errorMessage: typeof result === 'string' ? result : (rule.message || '验证失败'),
          };
        }
      }
    }

    return { isValid: true, errorMessage: '' };
  };

  // 执行验证
  const performValidation = (value: Date | null) => {
    const { isValid: valid, errorMessage: error } = validateValue(value);
    
    setIsValid(valid);
    setErrorMessage(error);
    
    // 通知父组件验证结果
    onValidationChange?.(name, valid, error);
    
    // 如果有表单实例，更新表单状态
    if (form) {
      form.setFieldValidation?.(name, valid, error);
    }
  };

  // 处理日期变化
  const handleDateChange = (date: Date | null) => {
    if (controlledValue === undefined) {
      setInternalValue(date);
    }
    
    onDateChange?.(date);
    
    // 如果验证触发器是onChange，立即验证
    if (validateTrigger === 'onChange') {
      performValidation(date);
    }
  };

  // 处理模态框打开
  const handlePress = () => {
    if (disabled) return;
    
    // 初始化临时值
    const dateToUse = currentValue || new Date();
    setTempYear(dateToUse.getFullYear());
    setTempMonth(dateToUse.getMonth() + 1);
    setTempDay(dateToUse.getDate());
    setTempHour(dateToUse.getHours());
    setTempMinute(dateToUse.getMinutes());
    
    setModalVisible(true);
    setFocused(true);
  };

  // 处理确认选择
  const handleConfirm = () => {
    const newDate = new Date(tempYear, tempMonth - 1, tempDay, tempHour, tempMinute);
    handleDateChange(newDate);
    setModalVisible(false);
    setFocused(false);
    setTouched(true);
    
    // 如果验证触发器是onBlur，执行验证
    if (validateTrigger === 'onBlur') {
      performValidation(newDate);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setModalVisible(false);
    setFocused(false);
    setTouched(true);
    
    // 如果验证触发器是onBlur，执行验证
    if (validateTrigger === 'onBlur') {
      performValidation(currentValue);
    }
  };

  // 获取显示文本
  const getDisplayText = (): string => {
    if (!currentValue) return placeholder;
    return formatDate(currentValue, format || '', mode);
  };

  // 生成年份选项
  const currentYear = new Date().getFullYear();
  const years = generateNumbers(currentYear - 50, currentYear + 10);
  const months = generateNumbers(1, 12);
  const days = generateNumbers(1, new Date(tempYear, tempMonth, 0).getDate());
  const hours = generateNumbers(0, 23);
  const minutes = generateNumbers(0, 59);

  const displayText = getDisplayText();
  const isPlaceholder = displayText === placeholder;
  const showError = touched && !isValid && showErrorMessage;

  // 暴露验证方法给父组件
  useEffect(() => {
    if (form) {
      form.registerField?.(name, {
        validate: () => performValidation(currentValue),
        getValue: () => currentValue,
        setValue: (value: Date | null) => {
          if (controlledValue === undefined) {
            setInternalValue(value);
          }
        },
      });
    }
  }, [name, form, currentValue, controlledValue]);

  const footer = (
    <ButtonContainer>
      <CustomButton
        variant="primary"
        size="medium"
        onPress={handleConfirm}
        flex={1}
      >
        确认
      </CustomButton>
      
      <CustomButton
        variant="outline"
        size="medium"
        onPress={handleCancel}
        flex={1}
      >
        取消
      </CustomButton>
    </ButtonContainer>
  );

  return (
    <DatePickerContainer>
      {label && (
        <XStack alignItems="center">
          <DatePickerLabel size={size}>
            {label}
          </DatePickerLabel>
          {required && <RequiredMark>*</RequiredMark>}
        </XStack>
      )}
      
      <DatePickerTrigger
        size={size}
        disabled={disabled}
        error={showError}
        focused={focused}
        onPress={handlePress}
      >
        <DatePickerValue
          size={size}
          placeholder={isPlaceholder}
        >
          {displayText}
        </DatePickerValue>
        
        <DatePickerIcon size={size}>
          📅
        </DatePickerIcon>
      </DatePickerTrigger>
      
      {showError && errorMessage && (
        <ErrorMessage>
          {errorMessage}
        </ErrorMessage>
      )}
      
      <CustomModal
        visible={modalVisible}
        onClose={handleCancel}
        title={label || '选择日期'}
        size="medium"
        footer={footer}
      >
        <DatePickerContent>
          {(mode === 'date' || mode === 'datetime') && (
            <DatePickerRow>
              <PickerColumn>
                <PickerTitle>年</PickerTitle>
                <PickerScroll showsVerticalScrollIndicator={false}>
                  {years.map(year => (
                    <PickerItem
                      key={year}
                      selected={year === tempYear}
                      onPress={() => setTempYear(year)}
                    >
                      {year}
                    </PickerItem>
                  ))}
                </PickerScroll>
              </PickerColumn>
              
              <PickerColumn>
                <PickerTitle>月</PickerTitle>
                <PickerScroll showsVerticalScrollIndicator={false}>
                  {months.map(month => (
                    <PickerItem
                      key={month}
                      selected={month === tempMonth}
                      onPress={() => setTempMonth(month)}
                    >
                      {month}
                    </PickerItem>
                  ))}
                </PickerScroll>
              </PickerColumn>
              
              <PickerColumn>
                <PickerTitle>日</PickerTitle>
                <PickerScroll showsVerticalScrollIndicator={false}>
                  {days.map(day => (
                    <PickerItem
                      key={day}
                      selected={day === tempDay}
                      onPress={() => setTempDay(day)}
                    >
                      {day}
                    </PickerItem>
                  ))}
                </PickerScroll>
              </PickerColumn>
            </DatePickerRow>
          )}
          
          {(mode === 'time' || mode === 'datetime') && (
            <DatePickerRow>
              <PickerColumn>
                <PickerTitle>时</PickerTitle>
                <PickerScroll showsVerticalScrollIndicator={false}>
                  {hours.map(hour => (
                    <PickerItem
                      key={hour}
                      selected={hour === tempHour}
                      onPress={() => setTempHour(hour)}
                    >
                      {String(hour).padStart(2, '0')}
                    </PickerItem>
                  ))}
                </PickerScroll>
              </PickerColumn>
              
              <PickerColumn>
                <PickerTitle>分</PickerTitle>
                <PickerScroll showsVerticalScrollIndicator={false}>
                  {minutes.map(minute => (
                    <PickerItem
                      key={minute}
                      selected={minute === tempMinute}
                      onPress={() => setTempMinute(minute)}
                    >
                      {String(minute).padStart(2, '0')}
                    </PickerItem>
                  ))}
                </PickerScroll>
              </PickerColumn>
            </DatePickerRow>
          )}
        </DatePickerContent>
      </CustomModal>
    </DatePickerContainer>
  );
};
