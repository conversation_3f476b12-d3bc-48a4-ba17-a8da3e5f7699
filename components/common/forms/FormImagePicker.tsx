/**
 * 表单图片选择器组件
 * 支持单张和多张图片选择、预览、删除
 */

import { ActionSheet } from '@/components/common/modals';
import React, { useEffect, useState } from 'react';
import { Text, View, XStack, YStack, styled } from 'tamagui';
import { ValidationRule } from './FormInput';

// 图片数据类型
export interface ImageItem {
  id: string;
  uri: string;
  name?: string;
  size?: number;
  type?: string;
}

// 表单图片选择器组件属性
export interface FormImagePickerProps {
  name: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  maxCount?: number;
  maxSize?: number; // 单位：MB
  value?: ImageItem | ImageItem[];
  defaultValue?: ImageItem | ImageItem[];
  onImageChange?: (images: ImageItem | ImageItem[] | null) => void;
  rules?: ValidationRule[];
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  showErrorMessage?: boolean;
  onValidationChange?: (name: string, isValid: boolean, errorMessage?: string) => void;
  form?: any;
  size?: 'small' | 'medium' | 'large';
  aspectRatio?: number;
  quality?: number;
  allowsEditing?: boolean;
}

// 样式化图片选择器容器
const ImagePickerContainer = styled(YStack, {
  name: 'ImagePickerContainer',
  gap: '$3',
});

const ImagePickerLabel = styled(Text, {
  name: 'ImagePickerLabel',
  fontSize: '$4',
  fontWeight: '500',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ImageGrid = styled(XStack, {
  name: 'ImageGrid',
  flexWrap: 'wrap',
  gap: '$3',
});

const ImageItemContainer = styled(View, {
  name: 'ImageItemContainer',
  position: 'relative',
  borderRadius: '$3',
  overflow: 'hidden',
  backgroundColor: '$backgroundHover',
  
  variants: {
    size: {
      small: {
        width: 60,
        height: 60,
      },
      
      medium: {
        width: 80,
        height: 80,
      },
      
      large: {
        width: 100,
        height: 100,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ImagePreview = styled(View, {
  name: 'ImagePreview',
  width: '100%',
  height: '100%',
  backgroundColor: '$backgroundHover',
  alignItems: 'center',
  justifyContent: 'center',
});

const ImagePlaceholder = styled(Text, {
  name: 'ImagePlaceholder',
  fontSize: '$6',
  color: '$placeholderColor',
});

const DeleteButton = styled(View, {
  name: 'DeleteButton',
  position: 'absolute',
  top: -8,
  right: -8,
  width: 20,
  height: 20,
  borderRadius: '$6',
  backgroundColor: '$error',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  zIndex: 1,
});

const DeleteIcon = styled(Text, {
  name: 'DeleteIcon',
  fontSize: '$2',
  color: '$white',
  fontWeight: 'bold',
});

const AddButton = styled(View, {
  name: 'AddButton',
  borderWidth: 2,
  borderColor: '$borderColor',
  borderStyle: 'dashed',
  borderRadius: '$3',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  
  variants: {
    size: {
      small: {
        width: 60,
        height: 60,
      },
      
      medium: {
        width: 80,
        height: 80,
      },
      
      large: {
        width: 100,
        height: 100,
      },
    },
    
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
  
  hoverStyle: {
    borderColor: '$primary',
    backgroundColor: '$backgroundHover',
  },
  
  pressStyle: {
    backgroundColor: '$backgroundPress',
  },
});

const AddIcon = styled(Text, {
  name: 'AddIcon',
  fontSize: '$6',
  color: '$placeholderColor',
});

const ErrorMessage = styled(Text, {
  name: 'ErrorMessage',
  fontSize: '$3',
  color: '$error',
  marginTop: '$1',
  lineHeight: '$3',
});

const RequiredMark = styled(Text, {
  name: 'RequiredMark',
  color: '$error',
  marginLeft: '$1',
});

const ImageInfo = styled(Text, {
  name: 'ImageInfo',
  fontSize: '$2',
  color: '$placeholderColor',
  marginTop: '$2',
});

export const FormImagePicker: React.FC<FormImagePickerProps> = ({
  name,
  label,
  required = false,
  disabled = false,
  multiple = false,
  maxCount = 9,
  maxSize = 10,
  value: controlledValue,
  defaultValue,
  onImageChange,
  rules = [],
  validateTrigger = 'onChange',
  showErrorMessage = true,
  onValidationChange,
  form,
  size = 'medium',
}) => {
  const [internalValue, setInternalValue] = useState<ImageItem | ImageItem[] | null>(
    controlledValue || defaultValue || (multiple ? [] : null)
  );
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isValid, setIsValid] = useState(true);
  const [touched, setTouched] = useState(false);

  const currentValue = controlledValue !== undefined ? controlledValue : internalValue;

  // 验证函数
  const validateValue = (value: ImageItem | ImageItem[] | null): { isValid: boolean; errorMessage: string } => {
    // 合并必填规则
    const allRules = required ? [{ required: true, message: '请选择图片' }, ...rules] : rules;
    
    for (const rule of allRules) {
      // 必填验证
      if (rule.required) {
        const isEmpty = multiple 
          ? !Array.isArray(value) || value.length === 0
          : !value;
        
        if (isEmpty) {
          return {
            isValid: false,
            errorMessage: rule.message || '请选择图片',
          };
        }
      }

      // 自定义验证
      if (rule.validator && value) {
        const stringValue = multiple && Array.isArray(value) 
          ? value.map(img => img.uri).join(',')
          : (value as ImageItem)?.uri || '';
        const result = rule.validator(stringValue);
        if (result !== true) {
          return {
            isValid: false,
            errorMessage: typeof result === 'string' ? result : (rule.message || '验证失败'),
          };
        }
      }
    }

    return { isValid: true, errorMessage: '' };
  };

  // 执行验证
  const performValidation = (value: ImageItem | ImageItem[] | null) => {
    const { isValid: valid, errorMessage: error } = validateValue(value);
    
    setIsValid(valid);
    setErrorMessage(error);
    
    // 通知父组件验证结果
    onValidationChange?.(name, valid, error);
    
    // 如果有表单实例，更新表单状态
    if (form) {
      form.setFieldValidation?.(name, valid, error);
    }
  };

  // 处理图片变化
  const handleImageChange = (newValue: ImageItem | ImageItem[] | null) => {
    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }
    
    onImageChange?.(newValue);
    
    // 如果验证触发器是onChange，立即验证
    if (validateTrigger === 'onChange') {
      performValidation(newValue);
    }
  };

  // 模拟图片选择（实际项目中需要集成相机和相册API）
  const mockImagePicker = (): Promise<ImageItem> => {
    return new Promise((resolve) => {
      // 模拟异步选择图片
      setTimeout(() => {
        const mockImage: ImageItem = {
          id: Date.now().toString(),
          uri: `https://picsum.photos/300/300?random=${Date.now()}`,
          name: `image_${Date.now()}.jpg`,
          size: Math.floor(Math.random() * 5) + 1, // 1-5MB
          type: 'image/jpeg',
        };
        resolve(mockImage);
      }, 1000);
    });
  };

  // 处理添加图片
  const handleAddImage = () => {
    if (disabled) return;
    setActionSheetVisible(true);
  };

  // 处理相机拍照
  const handleCamera = async () => {
    setActionSheetVisible(false);
    
    try {
      const image = await mockImagePicker();
      
      if (multiple && Array.isArray(currentValue)) {
        if (currentValue.length >= maxCount) {
          setErrorMessage(`最多只能选择${maxCount}张图片`);
          return;
        }
        handleImageChange([...currentValue, image]);
      } else {
        handleImageChange(image);
      }
      
      setTouched(true);
    } catch (error) {
      console.error('拍照失败:', error);
    }
  };

  // 处理相册选择
  const handleLibrary = async () => {
    setActionSheetVisible(false);
    
    try {
      const image = await mockImagePicker();
      
      if (multiple && Array.isArray(currentValue)) {
        if (currentValue.length >= maxCount) {
          setErrorMessage(`最多只能选择${maxCount}张图片`);
          return;
        }
        handleImageChange([...currentValue, image]);
      } else {
        handleImageChange(image);
      }
      
      setTouched(true);
    } catch (error) {
      console.error('选择图片失败:', error);
    }
  };

  // 处理删除图片
  const handleDeleteImage = (imageId: string) => {
    if (multiple && Array.isArray(currentValue)) {
      const newImages = currentValue.filter(img => img.id !== imageId);
      handleImageChange(newImages.length > 0 ? newImages : []);
    } else {
      handleImageChange(null);
    }
    
    setTouched(true);
  };

  // 获取当前图片列表
  const getImageList = (): ImageItem[] => {
    if (multiple && Array.isArray(currentValue)) {
      return currentValue;
    } else if (currentValue && !Array.isArray(currentValue)) {
      return [currentValue];
    }
    return [];
  };

  // 检查是否可以添加更多图片
  const canAddMore = (): boolean => {
    if (!multiple) {
      return !currentValue;
    }
    return Array.isArray(currentValue) ? currentValue.length < maxCount : true;
  };

  const imageList = getImageList();
  const showError = touched && !isValid && showErrorMessage;

  // 操作选项
  const actionItems = [
    {
      key: 'camera',
      title: '拍照',
      icon: <Text>📷</Text>,
      onPress: handleCamera,
    },
    {
      key: 'library',
      title: '从相册选择',
      icon: <Text>🖼️</Text>,
      onPress: handleLibrary,
    },
  ];

  // 暴露验证方法给父组件
  useEffect(() => {
    if (form) {
      form.registerField?.(name, {
        validate: () => performValidation(currentValue),
        getValue: () => currentValue,
        setValue: (value: ImageItem | ImageItem[] | null) => {
          if (controlledValue === undefined) {
            setInternalValue(value);
          }
        },
      });
    }
  }, [name, form, currentValue, controlledValue]);

  return (
    <ImagePickerContainer>
      {label && (
        <XStack alignItems="center">
          <ImagePickerLabel size={size}>
            {label}
          </ImagePickerLabel>
          {required && <RequiredMark>*</RequiredMark>}
        </XStack>
      )}
      
      <ImageGrid>
        {imageList.map((image) => (
          <ImageItemContainer key={image.id} size={size}>
            <ImagePreview>
              <ImagePlaceholder>🖼️</ImagePlaceholder>
            </ImagePreview>
            
            <DeleteButton onPress={() => handleDeleteImage(image.id)}>
              <DeleteIcon>×</DeleteIcon>
            </DeleteButton>
          </ImageItemContainer>
        ))}
        
        {canAddMore() && (
          <AddButton
            size={size}
            disabled={disabled}
            onPress={handleAddImage}
          >
            <AddIcon>+</AddIcon>
          </AddButton>
        )}
      </ImageGrid>
      
      {multiple && (
        <ImageInfo>
          已选择 {imageList.length}/{maxCount} 张图片，单张图片不超过 {maxSize}MB
        </ImageInfo>
      )}
      
      {showError && errorMessage && (
        <ErrorMessage>
          {errorMessage}
        </ErrorMessage>
      )}
      
      <ActionSheet
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        title="选择图片"
        actions={actionItems}
      />
    </ImagePickerContainer>
  );
};
