/**
 * 表单输入框组件
 * 基于CustomInput扩展，增加表单验证功能
 */

import { CustomInput, CustomInputProps } from '@/components/common/inputs';
import React, { useEffect, useState } from 'react';
import { Text, YStack, styled } from 'tamagui';

// 验证规则类型
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  validator?: (value: string) => boolean | string;
  message?: string;
}

// 表单输入框组件属性
export interface FormInputProps extends Omit<CustomInputProps, 'error' | 'helperText'> {
  name: string;
  rules?: ValidationRule[];
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  showErrorMessage?: boolean;
  onValidationChange?: (name: string, isValid: boolean, errorMessage?: string) => void;
  form?: any; // 表单实例，用于集成表单管理
}

// 样式化错误消息
const ErrorMessage = styled(Text, {
  name: 'ErrorMessage',
  fontSize: '$3',
  color: '$error',
  marginTop: '$2',
  lineHeight: '$3',
  
  variants: {
    visible: {
      true: {
        opacity: 1,
        height: 'auto',
      },
      false: {
        opacity: 0,
        height: 0,
      },
    },
  } as const,
  
  defaultVariants: {
    visible: true,
  },
});

// 内置验证规则
const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

const validateMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

const validatePattern = (value: string, pattern: RegExp): boolean => {
  return pattern.test(value);
};

// 常用验证规则
export const ValidationRules = {
  required: (message = '此字段为必填项'): ValidationRule => ({
    required: true,
    message,
  }),
  
  minLength: (length: number, message?: string): ValidationRule => ({
    minLength: length,
    message: message || `最少输入${length}个字符`,
  }),
  
  maxLength: (length: number, message?: string): ValidationRule => ({
    maxLength: length,
    message: message || `最多输入${length}个字符`,
  }),
  
  email: (message = '请输入有效的邮箱地址'): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message,
  }),
  
  phone: (message = '请输入有效的手机号码'): ValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message,
  }),
  
  idCard: (message = '请输入有效的身份证号码'): ValidationRule => ({
    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    message,
  }),
  
  password: (message = '密码必须包含字母和数字，长度6-20位'): ValidationRule => ({
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/,
    message,
  }),
  
  number: (message = '请输入有效的数字'): ValidationRule => ({
    pattern: /^\d+(\.\d+)?$/,
    message,
  }),
  
  integer: (message = '请输入有效的整数'): ValidationRule => ({
    pattern: /^\d+$/,
    message,
  }),
  
  custom: (validator: (value: string) => boolean | string, message = '输入格式不正确'): ValidationRule => ({
    validator,
    message,
  }),
};

export const FormInput: React.FC<FormInputProps> = ({
  name,
  rules = [],
  validateTrigger = 'onBlur',
  showErrorMessage = true,
  onValidationChange,
  form,
  value: controlledValue,
  onChangeText,
  onBlur,
  ...props
}) => {
  const [internalValue, setInternalValue] = useState(controlledValue || '');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isValid, setIsValid] = useState(true);
  const [touched, setTouched] = useState(false);

  const currentValue = controlledValue !== undefined ? controlledValue : internalValue;

  // 验证函数
  const validateValue = (value: string): { isValid: boolean; errorMessage: string } => {
    for (const rule of rules) {
      // 必填验证
      if (rule.required && !validateRequired(value)) {
        return {
          isValid: false,
          errorMessage: rule.message || '此字段为必填项',
        };
      }

      // 如果值为空且不是必填，跳过其他验证
      if (!value.trim() && !rule.required) {
        continue;
      }

      // 最小长度验证
      if (rule.minLength !== undefined && !validateMinLength(value, rule.minLength)) {
        return {
          isValid: false,
          errorMessage: rule.message || `最少输入${rule.minLength}个字符`,
        };
      }

      // 最大长度验证
      if (rule.maxLength !== undefined && !validateMaxLength(value, rule.maxLength)) {
        return {
          isValid: false,
          errorMessage: rule.message || `最多输入${rule.maxLength}个字符`,
        };
      }

      // 正则验证
      if (rule.pattern && !validatePattern(value, rule.pattern)) {
        return {
          isValid: false,
          errorMessage: rule.message || '输入格式不正确',
        };
      }

      // 自定义验证
      if (rule.validator) {
        const result = rule.validator(value);
        if (result !== true) {
          return {
            isValid: false,
            errorMessage: typeof result === 'string' ? result : (rule.message || '验证失败'),
          };
        }
      }
    }

    return { isValid: true, errorMessage: '' };
  };

  // 执行验证
  const performValidation = (value: string) => {
    const { isValid: valid, errorMessage: error } = validateValue(value);
    
    setIsValid(valid);
    setErrorMessage(error);
    
    // 通知父组件验证结果
    onValidationChange?.(name, valid, error);
    
    // 如果有表单实例，更新表单状态
    if (form) {
      form.setFieldValidation?.(name, valid, error);
    }
  };

  // 处理值变化
  const handleValueChange = (newValue: string) => {
    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }
    
    onChangeText?.(newValue);
    
    // 如果验证触发器是onChange，立即验证
    if (validateTrigger === 'onChange') {
      performValidation(newValue);
    }
  };

  // 处理失焦
  const handleBlur = (e?: any) => {
    setTouched(true);
    onBlur?.(e);

    // 如果验证触发器是onBlur，执行验证
    if (validateTrigger === 'onBlur') {
      performValidation(currentValue);
    }
  };

  // 当值变化时，如果已经触摸过且有错误，重新验证
  useEffect(() => {
    if (touched && !isValid && validateTrigger === 'onBlur') {
      performValidation(currentValue);
    }
  }, [currentValue, touched, isValid, validateTrigger]);

  // 暴露验证方法给父组件
  useEffect(() => {
    if (form) {
      form.registerField?.(name, {
        validate: () => performValidation(currentValue),
        getValue: () => currentValue,
        setValue: (value: string) => {
          if (controlledValue === undefined) {
            setInternalValue(value);
          }
        },
      });
    }
  }, [name, form, currentValue, controlledValue]);

  const showError = touched && !isValid && showErrorMessage;

  return (
    <YStack>
      <CustomInput
        {...props}
        value={currentValue}
        onChangeText={handleValueChange}
        onBlur={handleBlur}
        error={showError ? errorMessage : undefined}
      />

      {showError && errorMessage && (
        <ErrorMessage visible={showError}>
          {errorMessage}
        </ErrorMessage>
      )}
    </YStack>
  );
};

// 表单输入框的便捷方法
export const createFormInput = (defaultProps: Partial<FormInputProps>) => {
  return (props: FormInputProps) => (
    <FormInput {...defaultProps} {...props} />
  );
};
