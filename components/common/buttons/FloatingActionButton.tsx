/**
 * 浮动操作按钮组件
 */

import React from 'react';
import { Button, styled } from 'tamagui';

// 浮动按钮位置类型
export type FABPosition = 'bottom-right' | 'bottom-left' | 'bottom-center';

// 浮动按钮尺寸类型
export type FABSize = 'small' | 'medium' | 'large';

// 浮动按钮属性
export interface FloatingActionButtonProps extends Omit<React.ComponentProps<typeof Button>, 'size' | 'position'> {
  icon: React.ReactNode;
  size?: FABSize;
  position?: FABPosition;
  disabled?: boolean;
  loading?: boolean;
}

// 样式化浮动按钮
const StyledFAB = styled(But<PERSON>, {
  name: 'FloatingActionButton',
  position: 'absolute',
  borderRadius: '$12',
  backgroundColor: '$primary',
  color: '$white',
  cursor: 'pointer',
  alignItems: 'center',
  justifyContent: 'center',
  shadowColor: '$shadowColor',
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8,
  
  hoverStyle: {
    backgroundColor: '$primaryHover',
    scale: 1.05,
  },
  
  pressStyle: {
    backgroundColor: '$primaryHover',
    scale: 0.95,
  },
  
  variants: {
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },

    fabPosition: {
      'bottom-right': {
        bottom: 24,
        right: 24,
      },

      'bottom-left': {
        bottom: 24,
        left: 24,
      },

      'bottom-center': {
        bottom: 24,
        left: '50%',
        transform: [{ translateX: '-50%' }],
      },
    },

    size: {
      small: {
        width: 48,
        height: 48,
      },

      medium: {
        width: 56,
        height: 56,
      },

      large: {
        width: 64,
        height: 64,
      },
    },
  } as const,

  defaultVariants: {
    fabPosition: 'bottom-right',
    size: 'medium',
  },
});

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  size = 'medium',
  position = 'bottom-right',
  disabled = false,
  loading = false,
  onPress,
  ...props
}) => {
  const handlePress = (event: any) => {
    if (loading || disabled) {
      return;
    }
    onPress?.(event);
  };

  return (
    <StyledFAB
      size={size}
      fabPosition={position}
      disabled={disabled || loading}
      onPress={handlePress}
      {...props}
    >
      {icon}
    </StyledFAB>
  );
};
