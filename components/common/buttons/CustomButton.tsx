/**
 * 自定义按钮组件
 */

import React from 'react';
import { But<PERSON>, Spinner, styled, Text } from 'tamagui';

// 按钮变体类型
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';

// 按钮尺寸类型
export type ButtonSize = 'small' | 'medium' | 'large';

// 自定义按钮属性
export interface CustomButtonProps extends Omit<React.ComponentProps<typeof Button>, 'size' | 'variant'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
}

// 样式化按钮组件
const StyledButton = styled(Button, {
  name: 'CustomButton',
  borderRadius: '$4',
  fontWeight: '600',
  cursor: 'pointer',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '$2',
  
  // 禁用状态
  variants: {
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
    
    fullWidth: {
      true: {
        width: '100%',
      },
    },
    
    variant: {
      primary: {
        backgroundColor: '$primary',
        color: '$white',
        borderColor: '$primary',
        
        hoverStyle: {
          backgroundColor: '$primaryHover',
          borderColor: '$primaryHover',
        },
        
        pressStyle: {
          backgroundColor: '$primaryHover',
          borderColor: '$primaryHover',
          scale: 0.98,
        },
      },
      
      secondary: {
        backgroundColor: '$secondary',
        color: '$white',
        borderColor: '$secondary',
        
        hoverStyle: {
          backgroundColor: '$secondaryHover',
          borderColor: '$secondaryHover',
        },
        
        pressStyle: {
          backgroundColor: '$secondaryHover',
          borderColor: '$secondaryHover',
          scale: 0.98,
        },
      },
      
      outline: {
        backgroundColor: 'transparent',
        color: '$primary',
        borderColor: '$primary',
        borderWidth: 1,
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
          scale: 0.98,
        },
      },
      
      ghost: {
        backgroundColor: 'transparent',
        color: '$primary',
        borderColor: 'transparent',
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
          scale: 0.98,
        },
      },
      
      danger: {
        backgroundColor: '$error',
        color: '$white',
        borderColor: '$error',
        
        hoverStyle: {
          backgroundColor: '$errorHover',
          borderColor: '$errorHover',
        },
        
        pressStyle: {
          backgroundColor: '$errorHover',
          borderColor: '$errorHover',
          scale: 0.98,
        },
      },
    },
    
    size: {
      small: {
        height: 32,
        paddingHorizontal: '$3',
        fontSize: '$3',
      },
      
      medium: {
        height: 40,
        paddingHorizontal: '$4',
        fontSize: '$4',
      },
      
      large: {
        height: 48,
        paddingHorizontal: '$5',
        fontSize: '$5',
      },
    },
  } as const,

  defaultVariants: {
    variant: 'primary',
    size: 'medium',
  },
});

export const CustomButton: React.FC<CustomButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  children,
  onPress,
  ...props
}) => {
  const handlePress = (event: any) => {
    if (loading || disabled) {
      return;
    }
    onPress?.(event);
  };

  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      fullWidth={fullWidth}
      onPress={handlePress}
      {...props}
    >
      {loading && (
        <Spinner 
          size="small" 
          color={variant === 'outline' || variant === 'ghost' ? '$primary' : '$white'} 
        />
      )}
      {typeof children === 'string' ? (
        <Text
          color={
            variant === 'outline' || variant === 'ghost' 
              ? '$primary' 
              : '$white'
          }
          fontSize={size === 'small' ? '$3' : size === 'large' ? '$5' : '$4'}
          fontWeight="600"
        >
          {children}
        </Text>
      ) : (
        children
      )}
    </StyledButton>
  );
};
