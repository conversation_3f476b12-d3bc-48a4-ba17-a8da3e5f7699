/**
 * 空状态组件
 */

import React from 'react';
import { Button, styled, Text, YStack } from 'tamagui';

// 空状态属性
export interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  size?: 'small' | 'medium' | 'large';
}

// 样式化容器
const EmptyContainer = styled(YStack, {
  name: 'EmptyContainer',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '$4',
  
  variants: {
    size: {
      small: {
        padding: '$4',
      },
      
      medium: {
        padding: '$6',
      },
      
      large: {
        padding: '$8',
      },
    },
  } as const,

  defaultVariants: {
    size: 'medium',
  },
});

const IconContainer = styled(YStack, {
  name: 'IconContainer',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0.6,

  variants: {
    size: {
      small: {
        width: 48,
        height: 48,
      },

      medium: {
        width: 64,
        height: 64,
      },

      large: {
        width: 80,
        height: 80,
      },
    },
  } as const,

  defaultVariants: {
    size: 'medium',
  },
});

const EmptyTitle = styled(Text, {
  name: 'EmptyTitle',
  fontWeight: '600',
  textAlign: 'center',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$4',
      },
      
      medium: {
        fontSize: '$5',
      },
      
      large: {
        fontSize: '$6',
      },
    },
  } as const,

  defaultVariants: {
    size: 'medium',
  },
});

const EmptyDescription = styled(Text, {
  name: 'EmptyDescription',
  textAlign: 'center',
  color: '$placeholderColor',
  lineHeight: '$4',

  variants: {
    size: {
      small: {
        fontSize: '$3',
        maxWidth: 200,
      },

      medium: {
        fontSize: '$4',
        maxWidth: 280,
      },

      large: {
        fontSize: '$5',
        maxWidth: 320,
      },
    },
  } as const,

  defaultVariants: {
    size: 'medium',
  },
});

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionText,
  onAction,
  size = 'medium',
}) => {
  return (
    <EmptyContainer size={size}>
      {icon && (
        <IconContainer size={size}>
          {icon}
        </IconContainer>
      )}
      
      <YStack gap="$2" alignItems="center">
        <EmptyTitle size={size}>
          {title}
        </EmptyTitle>
        
        {description && (
          <EmptyDescription size={size}>
            {description}
          </EmptyDescription>
        )}
      </YStack>
      
      {actionText && onAction && (
        <Button
          variant="outlined"
          size={size === 'small' ? '$3' : size === 'large' ? '$5' : '$4'}
          onPress={onAction}
        >
          {actionText}
        </Button>
      )}
    </EmptyContainer>
  );
};
