/**
 * 操作表单组件
 * 从底部弹出的操作选择器
 */

import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Sheet, Text, XStack, YStack, styled } from 'tamagui';

// 操作项数据类型
export interface ActionItem {
  key: string;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  destructive?: boolean;
  disabled?: boolean;
  onPress?: () => void;
}

// 操作表单组件属性
export interface ActionSheetProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  actions: ActionItem[];
  cancelText?: string;
  showCancel?: boolean;
  safeArea?: boolean;
}

// 样式化操作表单容器
const ActionSheetContainer = styled(YStack, {
  name: 'ActionSheetContainer',
  backgroundColor: '$background',
  borderTopLeftRadius: '$4',
  borderTopRightRadius: '$4',
  overflow: 'hidden',
  maxHeight: '80%',
});

const ActionSheetHeader = styled(YStack, {
  name: 'ActionSheetHeader',
  alignItems: 'center',
  padding: '$4',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  gap: '$2',
});

const ActionSheetTitle = styled(Text, {
  name: 'ActionSheetTitle',
  fontSize: '$4',
  fontWeight: '600',
  color: '$color',
  textAlign: 'center',
});

const ActionSheetMessage = styled(Text, {
  name: 'ActionSheetMessage',
  fontSize: '$3',
  color: '$placeholderColor',
  textAlign: 'center',
  lineHeight: '$4',
});

const ActionList = styled(YStack, {
  name: 'ActionList',
  flex: 1,
});

const ActionItem = styled(XStack, {
  name: 'ActionItem',
  alignItems: 'center',
  paddingHorizontal: '$4',
  paddingVertical: '$4',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  cursor: 'pointer',
  
  variants: {
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
    
    destructive: {
      true: {
        // 危险操作样式
      },
    },
  } as const,
  
  hoverStyle: {
    backgroundColor: '$backgroundHover',
  },
  
  pressStyle: {
    backgroundColor: '$backgroundPress',
  },
});

const ActionIcon = styled(YStack, {
  name: 'ActionIcon',
  alignItems: 'center',
  justifyContent: 'center',
  width: 24,
  height: 24,
  marginRight: '$3',
});

const ActionContent = styled(YStack, {
  name: 'ActionContent',
  flex: 1,
});

const ActionTitle = styled(Text, {
  name: 'ActionTitle',
  fontSize: '$4',
  fontWeight: '500',
  
  variants: {
    destructive: {
      true: {
        color: '$error',
      },
      false: {
        color: '$color',
      },
    },
    
    disabled: {
      true: {
        color: '$placeholderColor',
      },
    },
  } as const,
});

const ActionSubtitle = styled(Text, {
  name: 'ActionSubtitle',
  fontSize: '$3',
  color: '$placeholderColor',
  marginTop: '$1',
});

const CancelButton = styled(XStack, {
  name: 'CancelButton',
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: '$4',
  backgroundColor: '$backgroundHover',
  cursor: 'pointer',
  
  hoverStyle: {
    backgroundColor: '$backgroundPress',
  },
  
  pressStyle: {
    backgroundColor: '$borderColor',
  },
});

const CancelText = styled(Text, {
  name: 'CancelText',
  fontSize: '$4',
  fontWeight: '600',
  color: '$color',
});

const ActionItemComponent: React.FC<{
  action: ActionItem;
  onPress: () => void;
}> = ({ action, onPress }) => {
  const handlePress = () => {
    if (!action.disabled) {
      action.onPress?.();
      onPress();
    }
  };

  return (
    <ActionItem
      disabled={action.disabled}
      destructive={action.destructive}
      onPress={handlePress}
    >
      {action.icon && (
        <ActionIcon>
          {action.icon}
        </ActionIcon>
      )}
      
      <ActionContent>
        <ActionTitle
          destructive={action.destructive}
          disabled={action.disabled}
        >
          {action.title}
        </ActionTitle>
        
        {action.subtitle && (
          <ActionSubtitle>
            {action.subtitle}
          </ActionSubtitle>
        )}
      </ActionContent>
    </ActionItem>
  );
};

export const ActionSheet: React.FC<ActionSheetProps> = ({
  visible,
  onClose,
  title,
  message,
  actions,
  cancelText = '取消',
  showCancel = true,
  safeArea = true,
}) => {
  const insets = useSafeAreaInsets();
  
  const hasHeader = Boolean(title || message);
  const safeAreaPadding = safeArea ? { paddingBottom: insets.bottom } : {};

  const handleActionPress = () => {
    onClose();
  };

  return (
    <Sheet
      modal
      open={visible}
      onOpenChange={(open: boolean) => {
        if (!open) {
          onClose();
        }
      }}
      snapPoints={[50, 85]}
      position={0}
      dismissOnSnapToBottom
      dismissOnOverlayPress
      animation="medium"
    >
      <Sheet.Overlay 
        backgroundColor="rgba(0, 0, 0, 0.5)"
        enterStyle={{ opacity: 0 }}
        exitStyle={{ opacity: 0 }}
      />
      
      <Sheet.Handle />
      
      <Sheet.Frame flex={1} justifyContent="flex-end">
        <ActionSheetContainer style={safeAreaPadding}>
          {hasHeader && (
            <ActionSheetHeader>
              {title && (
                <ActionSheetTitle>
                  {title}
                </ActionSheetTitle>
              )}
              
              {message && (
                <ActionSheetMessage>
                  {message}
                </ActionSheetMessage>
              )}
            </ActionSheetHeader>
          )}
          
          <ActionList>
            {actions.map((action) => (
              <ActionItemComponent
                key={action.key}
                action={action}
                onPress={handleActionPress}
              />
            ))}
          </ActionList>
          
          {showCancel && (
            <CancelButton onPress={onClose}>
              <CancelText>
                {cancelText}
              </CancelText>
            </CancelButton>
          )}
        </ActionSheetContainer>
      </Sheet.Frame>
    </Sheet>
  );
};
