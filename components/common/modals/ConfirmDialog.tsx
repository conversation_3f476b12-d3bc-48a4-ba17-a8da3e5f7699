/**
 * 确认对话框组件
 * 基于CustomModal实现的确认/取消对话框
 */

import { CustomButton } from '@/components/common/buttons';
import React from 'react';
import { Text, YStack, styled } from 'tamagui';
import { CustomModal, CustomModalProps } from './CustomModal';

// 确认对话框组件属性
export interface ConfirmDialogProps extends Omit<CustomModalProps, 'children' | 'footer'> {
  message: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonVariant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  cancelButtonVariant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  onConfirm?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  icon?: React.ReactNode;
  type?: 'default' | 'warning' | 'danger' | 'success' | 'info';
}

// 样式化消息容器
const MessageContainer = styled(YStack, {
  name: 'MessageContainer',
  alignItems: 'center',
  gap: '$3',
  paddingVertical: '$2',
});

const IconContainer = styled(YStack, {
  name: 'IconContainer',
  alignItems: 'center',
  justifyContent: 'center',
  width: 48,
  height: 48,
  borderRadius: '$6',
  marginBottom: '$2',
  
  variants: {
    type: {
      default: {
        backgroundColor: '$backgroundHover',
      },
      
      warning: {
        backgroundColor: '$warning',
      },
      
      danger: {
        backgroundColor: '$error',
      },
      
      success: {
        backgroundColor: '$success',
      },
      
      info: {
        backgroundColor: '$primary',
      },
    },
  } as const,
  
  defaultVariants: {
    type: 'default',
  },
});

const MessageText = styled(Text, {
  name: 'MessageText',
  fontSize: '$4',
  fontWeight: '500',
  color: '$color',
  textAlign: 'center',
  lineHeight: '$5',
});

const DescriptionText = styled(Text, {
  name: 'DescriptionText',
  fontSize: '$3',
  color: '$placeholderColor',
  textAlign: 'center',
  lineHeight: '$4',
  marginTop: '$2',
});

const ButtonContainer = styled(YStack, {
  name: 'ButtonContainer',
  gap: '$3',
  width: '100%',
});

// 获取默认图标
const getDefaultIcon = (type: ConfirmDialogProps['type']) => {
  switch (type) {
    case 'warning':
      return <Text fontSize="$6">⚠️</Text>;
    case 'danger':
      return <Text fontSize="$6">❌</Text>;
    case 'success':
      return <Text fontSize="$6">✅</Text>;
    case 'info':
      return <Text fontSize="$6">ℹ️</Text>;
    default:
      return <Text fontSize="$6">❓</Text>;
  }
};

// 获取确认按钮的默认变体
const getConfirmButtonVariant = (type: ConfirmDialogProps['type']) => {
  switch (type) {
    case 'danger':
      return 'danger';
    case 'warning':
      return 'outline';
    default:
      return 'primary';
  }
};

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  message,
  description,
  confirmText = '确认',
  cancelText = '取消',
  confirmButtonVariant,
  cancelButtonVariant = 'outline',
  onConfirm,
  onCancel,
  onClose,
  loading = false,
  icon,
  type = 'default',
  ...modalProps
}) => {
  const handleConfirm = () => {
    onConfirm?.();
    onClose();
  };

  const handleCancel = () => {
    onCancel?.();
    onClose();
  };

  const defaultConfirmVariant = confirmButtonVariant || getConfirmButtonVariant(type);
  const iconToShow = icon || getDefaultIcon(type);

  const footer = (
    <ButtonContainer>
      <CustomButton
        variant={defaultConfirmVariant}
        size="medium"
        loading={loading}
        onPress={handleConfirm}
        fullWidth
      >
        {confirmText}
      </CustomButton>
      
      <CustomButton
        variant={cancelButtonVariant}
        size="medium"
        disabled={loading}
        onPress={handleCancel}
        fullWidth
      >
        {cancelText}
      </CustomButton>
    </ButtonContainer>
  );

  return (
    <CustomModal
      {...modalProps}
      onClose={onClose}
      size="small"
      position="center"
      closable={false}
      footer={footer}
    >
      <MessageContainer>
        {iconToShow && (
          <IconContainer type={type}>
            {iconToShow}
          </IconContainer>
        )}
        
        <MessageText>
          {message}
        </MessageText>
        
        {description && (
          <DescriptionText>
            {description}
          </DescriptionText>
        )}
      </MessageContainer>
    </CustomModal>
  );
};

// 便捷方法：显示确认对话框
export const showConfirmDialog = (props: Omit<ConfirmDialogProps, 'visible'>) => {
  return new Promise<boolean>((resolve) => {
    const handleConfirm = () => {
      props.onConfirm?.();
      resolve(true);
    };

    const handleCancel = () => {
      props.onCancel?.();
      resolve(false);
    };

    // 这里需要配合全局状态管理来显示对话框
    // 实际实现中可能需要使用Context或状态管理库
    console.log('ConfirmDialog:', { ...props, onConfirm: handleConfirm, onCancel: handleCancel });
  });
};
