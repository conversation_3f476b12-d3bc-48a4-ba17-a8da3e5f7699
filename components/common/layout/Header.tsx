/**
 * 页面头部组件
 * 提供统一的导航栏布局和样式
 */

import React from 'react';
import { XStack, YStack, Text, styled, GetProps } from 'tamagui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IconButton } from '../buttons';

// 头部组件属性
export interface HeaderProps extends Omit<GetProps<typeof XStack>, 'children'> {
  title?: string;
  subtitle?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  leftAction?: () => void;
  rightAction?: () => void;
  centerContent?: React.ReactNode;
  safeArea?: boolean;
  transparent?: boolean;
  size?: 'small' | 'medium' | 'large';
  shadow?: boolean;
}

// 样式化头部容器
const HeaderContainer = styled(YStack, {
  name: 'HeaderContainer',
  backgroundColor: '$background',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  zIndex: 100,
  
  variants: {
    transparent: {
      true: {
        backgroundColor: 'transparent',
        borderBottomWidth: 0,
      },
    },
    
    shadow: {
      true: {
        shadowColor: '$shadowColor',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 4,
      },
    },
    
    size: {
      small: {
        minHeight: 44,
      },
      
      medium: {
        minHeight: 56,
      },
      
      large: {
        minHeight: 64,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
    shadow: true,
  },
});

const HeaderContent = styled(XStack, {
  name: 'HeaderContent',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal: '$4',
  flex: 1,
  
  variants: {
    size: {
      small: {
        paddingVertical: '$2',
      },
      
      medium: {
        paddingVertical: '$3',
      },
      
      large: {
        paddingVertical: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const HeaderLeft = styled(XStack, {
  name: 'HeaderLeft',
  alignItems: 'center',
  flex: 1,
  justifyContent: 'flex-start',
});

const HeaderCenter = styled(XStack, {
  name: 'HeaderCenter',
  alignItems: 'center',
  flex: 2,
  justifyContent: 'center',
});

const HeaderRight = styled(XStack, {
  name: 'HeaderRight',
  alignItems: 'center',
  flex: 1,
  justifyContent: 'flex-end',
});

const HeaderTitle = styled(Text, {
  name: 'HeaderTitle',
  fontWeight: '600',
  color: '$color',
  textAlign: 'center',
  
  variants: {
    size: {
      small: {
        fontSize: '$4',
      },
      
      medium: {
        fontSize: '$5',
      },
      
      large: {
        fontSize: '$6',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const HeaderSubtitle = styled(Text, {
  name: 'HeaderSubtitle',
  color: '$placeholderColor',
  textAlign: 'center',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

export const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  leftAction,
  rightAction,
  centerContent,
  safeArea = true,
  transparent = false,
  size = 'medium',
  shadow = true,
  ...props
}) => {
  const insets = useSafeAreaInsets();
  
  const safeAreaPadding = safeArea ? { paddingTop: insets.top } : {};

  return (
    <HeaderContainer
      transparent={transparent}
      shadow={shadow}
      size={size}
      style={safeAreaPadding}
      {...props}
    >
      <HeaderContent size={size}>
        <HeaderLeft>
          {leftIcon && (
            <IconButton
              icon={leftIcon}
              variant="ghost"
              size={size === 'small' ? 'small' : 'medium'}
              onPress={leftAction}
            />
          )}
        </HeaderLeft>
        
        <HeaderCenter>
          {centerContent || (
            <YStack alignItems="center" gap="$1">
              {title && (
                <HeaderTitle size={size} numberOfLines={1}>
                  {title}
                </HeaderTitle>
              )}
              {subtitle && (
                <HeaderSubtitle size={size} numberOfLines={1}>
                  {subtitle}
                </HeaderSubtitle>
              )}
            </YStack>
          )}
        </HeaderCenter>
        
        <HeaderRight>
          {rightIcon && (
            <IconButton
              icon={rightIcon}
              variant="ghost"
              size={size === 'small' ? 'small' : 'medium'}
              onPress={rightAction}
            />
          )}
        </HeaderRight>
      </HeaderContent>
    </HeaderContainer>
  );
};
