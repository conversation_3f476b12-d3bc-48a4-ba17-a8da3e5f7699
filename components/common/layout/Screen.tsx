/**
 * 屏幕容器组件
 * 提供统一的页面布局和安全区域处理
 */

import React from 'react';
import { YStack, styled, GetProps } from 'tamagui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 屏幕组件属性
export interface ScreenProps extends Omit<GetProps<typeof YStack>, 'padding'> {
  safeArea?: boolean | 'top' | 'bottom' | 'horizontal' | 'vertical';
  scrollable?: boolean;
  centered?: boolean;
  preset?: 'default' | 'scroll' | 'fixed';
}

// 样式化屏幕容器
const ScreenContainer = styled(YStack, {
  name: 'ScreenContainer',
  flex: 1,
  backgroundColor: '$background',
  
  variants: {
    preset: {
      default: {
        flex: 1,
      },
      
      scroll: {
        flex: 1,
      },
      
      fixed: {
        flex: 1,
        position: 'relative',
      },
    },
    
    centered: {
      true: {
        justifyContent: 'center',
        alignItems: 'center',
      },
    },
    
    scrollable: {
      true: {
        // 滚动样式将通过ScrollView处理
      },
    },
  } as const,
  
  defaultVariants: {
    preset: 'default',
  },
});

const SafeAreaContainer = styled(YStack, {
  name: 'SafeAreaContainer',
  flex: 1,
});

export const Screen: React.FC<ScreenProps> = ({
  children,
  safeArea = true,
  scrollable = false,
  centered = false,
  preset = 'default',
  ...props
}) => {
  const insets = useSafeAreaInsets();
  
  // 计算安全区域内边距
  const getSafeAreaPadding = () => {
    if (!safeArea) return {};
    
    const padding = {
      paddingTop: 0,
      paddingBottom: 0,
      paddingLeft: 0,
      paddingRight: 0,
    };
    
    if (safeArea === true) {
      padding.paddingTop = insets.top;
      padding.paddingBottom = insets.bottom;
      padding.paddingLeft = insets.left;
      padding.paddingRight = insets.right;
    } else if (safeArea === 'top') {
      padding.paddingTop = insets.top;
    } else if (safeArea === 'bottom') {
      padding.paddingBottom = insets.bottom;
    } else if (safeArea === 'horizontal') {
      padding.paddingLeft = insets.left;
      padding.paddingRight = insets.right;
    } else if (safeArea === 'vertical') {
      padding.paddingTop = insets.top;
      padding.paddingBottom = insets.bottom;
    }
    
    return padding;
  };

  const safeAreaPadding = getSafeAreaPadding();

  if (scrollable) {
    // 如果需要滚动，使用ScrollView包装
    const { ScrollView } = require('react-native');
    
    return (
      <ScreenContainer preset={preset} centered={centered} {...props}>
        <SafeAreaContainer style={safeAreaPadding}>
          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={centered ? { flexGrow: 1, justifyContent: 'center' } : { flexGrow: 1 }}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {children}
          </ScrollView>
        </SafeAreaContainer>
      </ScreenContainer>
    );
  }

  return (
    <ScreenContainer preset={preset} centered={centered} {...props}>
      <SafeAreaContainer style={safeAreaPadding}>
        {children}
      </SafeAreaContainer>
    </ScreenContainer>
  );
};
