/**
 * 卡片组件
 * 提供统一的内容容器和布局样式
 */

import React from 'react';
import { YStack, XStack, Text, styled, GetProps } from 'tamagui';

// 卡片组件属性
export interface CardProps extends GetProps<typeof YStack> {
  title?: string;
  subtitle?: string;
  headerAction?: React.ReactNode;
  footer?: React.ReactNode;
  variant?: 'default' | 'outlined' | 'elevated' | 'filled';
  size?: 'small' | 'medium' | 'large';
  pressable?: boolean;
  onPress?: () => void;
}

// 样式化卡片容器
const CardContainer = styled(YStack, {
  name: 'CardContainer',
  borderRadius: '$4',
  backgroundColor: '$background',
  overflow: 'hidden',
  
  variants: {
    variant: {
      default: {
        backgroundColor: '$background',
      },
      
      outlined: {
        backgroundColor: '$background',
        borderWidth: 1,
        borderColor: '$borderColor',
      },
      
      elevated: {
        backgroundColor: '$background',
        shadowColor: '$shadowColor',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      },
      
      filled: {
        backgroundColor: '$backgroundHover',
      },
    },
    
    size: {
      small: {
        borderRadius: '$3',
      },
      
      medium: {
        borderRadius: '$4',
      },
      
      large: {
        borderRadius: '$5',
      },
    },
    
    pressable: {
      true: {
        cursor: 'pointer',
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
          transform: [{ scale: 1.02 }],
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
          transform: [{ scale: 0.98 }],
        },
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'default',
    size: 'medium',
  },
});

const CardHeader = styled(XStack, {
  name: 'CardHeader',
  alignItems: 'center',
  justifyContent: 'space-between',
  
  variants: {
    size: {
      small: {
        padding: '$3',
        paddingBottom: '$2',
      },
      
      medium: {
        padding: '$4',
        paddingBottom: '$2',
      },
      
      large: {
        padding: '$5',
        paddingBottom: '$3',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const CardContent = styled(YStack, {
  name: 'CardContent',
  
  variants: {
    size: {
      small: {
        paddingHorizontal: '$3',
        paddingBottom: '$3',
      },
      
      medium: {
        paddingHorizontal: '$4',
        paddingBottom: '$4',
      },
      
      large: {
        paddingHorizontal: '$5',
        paddingBottom: '$5',
      },
    },
    
    hasHeader: {
      true: {
        // 如果有头部，减少顶部内边距
      },
      false: {
        // 如果没有头部，添加顶部内边距
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const CardFooter = styled(XStack, {
  name: 'CardFooter',
  alignItems: 'center',
  justifyContent: 'flex-end',
  borderTopWidth: 1,
  borderTopColor: '$borderColor',
  
  variants: {
    size: {
      small: {
        padding: '$3',
        paddingTop: '$3',
      },
      
      medium: {
        padding: '$4',
        paddingTop: '$3',
      },
      
      large: {
        padding: '$5',
        paddingTop: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const CardTitle = styled(Text, {
  name: 'CardTitle',
  fontWeight: '600',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$4',
      },
      
      medium: {
        fontSize: '$5',
      },
      
      large: {
        fontSize: '$6',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const CardSubtitle = styled(Text, {
  name: 'CardSubtitle',
  color: '$placeholderColor',
  marginTop: '$1',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

export const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  headerAction,
  footer,
  variant = 'default',
  size = 'medium',
  pressable = false,
  onPress,
  ...props
}) => {
  const hasHeader = Boolean(title || subtitle || headerAction);
  
  // 计算内容的内边距样式
  const getContentPadding = () => {
    if (hasHeader && footer) {
      return { paddingTop: 0, paddingBottom: 0 };
    } else if (hasHeader) {
      return { paddingTop: 0 };
    } else if (footer) {
      return { paddingBottom: 0 };
    }
    return {};
  };

  return (
    <CardContainer
      variant={variant}
      size={size}
      pressable={pressable}
      onPress={pressable ? onPress : undefined}
      {...props}
    >
      {hasHeader && (
        <CardHeader size={size}>
          <YStack flex={1}>
            {title && (
              <CardTitle size={size}>
                {title}
              </CardTitle>
            )}
            {subtitle && (
              <CardSubtitle size={size}>
                {subtitle}
              </CardSubtitle>
            )}
          </YStack>
          {headerAction}
        </CardHeader>
      )}
      
      <CardContent 
        size={size} 
        hasHeader={hasHeader}
        style={getContentPadding()}
      >
        {children}
      </CardContent>
      
      {footer && (
        <CardFooter size={size}>
          {footer}
        </CardFooter>
      )}
    </CardContainer>
  );
};
