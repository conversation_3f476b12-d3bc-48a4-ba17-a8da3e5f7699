/**
 * 标签栏组件
 * 提供底部导航和标签切换功能
 */

import React, { useState } from 'react';
import { XStack, YStack, Text, View, styled, GetProps } from 'tamagui';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 标签项数据类型
export interface TabItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  activeIcon?: React.ReactNode;
  badge?: string | number;
  disabled?: boolean;
}

// 标签栏组件属性
export interface TabBarProps extends Omit<GetProps<typeof XStack>, 'children'> {
  tabs: TabItem[];
  activeKey?: string;
  defaultActiveKey?: string;
  onTabChange?: (key: string) => void;
  variant?: 'bottom' | 'top' | 'segmented';
  size?: 'small' | 'medium' | 'large';
  safeArea?: boolean;
  showLabels?: boolean;
}

// 标签项组件属性
export interface TabBarItemProps extends GetProps<typeof YStack> {
  tab: TabItem;
  active?: boolean;
  onPress?: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'bottom' | 'top' | 'segmented';
  showLabel?: boolean;
}

// 样式化标签栏容器
const TabBarContainer = styled(XStack, {
  name: 'TabBarContainer',
  backgroundColor: '$background',
  borderTopWidth: 1,
  borderTopColor: '$borderColor',
  
  variants: {
    variant: {
      bottom: {
        borderTopWidth: 1,
        borderTopColor: '$borderColor',
      },
      
      top: {
        borderBottomWidth: 1,
        borderBottomColor: '$borderColor',
        borderTopWidth: 0,
      },
      
      segmented: {
        backgroundColor: '$backgroundHover',
        borderRadius: '$4',
        padding: '$1',
        borderTopWidth: 0,
      },
    },
    
    size: {
      small: {
        minHeight: 48,
      },
      
      medium: {
        minHeight: 56,
      },
      
      large: {
        minHeight: 64,
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'bottom',
    size: 'medium',
  },
});

const TabBarItem = styled(YStack, {
  name: 'TabBarItem',
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  position: 'relative',
  
  variants: {
    active: {
      true: {
        // 激活状态样式将通过variant处理
      },
    },
    
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
    
    variant: {
      bottom: {
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
        },
      },
      
      top: {
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
        },
      },
      
      segmented: {
        borderRadius: '$3',
        margin: '$0.5',
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
        },
      },
    },
    
    size: {
      small: {
        paddingVertical: '$2',
        paddingHorizontal: '$1',
      },
      
      medium: {
        paddingVertical: '$3',
        paddingHorizontal: '$2',
      },
      
      large: {
        paddingVertical: '$4',
        paddingHorizontal: '$3',
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'bottom',
    size: 'medium',
  },
});

const TabIcon = styled(View, {
  name: 'TabIcon',
  alignItems: 'center',
  justifyContent: 'center',
  
  variants: {
    size: {
      small: {
        width: 20,
        height: 20,
      },
      
      medium: {
        width: 24,
        height: 24,
      },
      
      large: {
        width: 28,
        height: 28,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const TabLabel = styled(Text, {
  name: 'TabLabel',
  textAlign: 'center',
  fontWeight: '500',
  
  variants: {
    active: {
      true: {
        color: '$primary',
      },
      false: {
        color: '$placeholderColor',
      },
    },
    
    size: {
      small: {
        fontSize: '$2',
        marginTop: '$1',
      },
      
      medium: {
        fontSize: '$3',
        marginTop: '$1',
      },
      
      large: {
        fontSize: '$4',
        marginTop: '$2',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const TabBadge = styled(View, {
  name: 'TabBadge',
  position: 'absolute',
  top: 0,
  right: '25%',
  backgroundColor: '$error',
  borderRadius: '$6',
  minWidth: 16,
  height: 16,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: '$1',
});

const BadgeText = styled(Text, {
  name: 'BadgeText',
  color: '$white',
  fontSize: '$1',
  fontWeight: '600',
});

const ActiveIndicator = styled(View, {
  name: 'ActiveIndicator',
  position: 'absolute',
  backgroundColor: '$primary',
  
  variants: {
    variant: {
      bottom: {
        top: 0,
        left: '25%',
        right: '25%',
        height: 2,
      },
      
      top: {
        bottom: 0,
        left: '25%',
        right: '25%',
        height: 2,
      },
      
      segmented: {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: '$background',
        borderRadius: '$3',
        shadowColor: '$shadowColor',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      },
    },
  } as const,
});

// 标签项组件
const TabBarItemComponent: React.FC<TabBarItemProps> = ({
  tab,
  active = false,
  onPress,
  size = 'medium',
  variant = 'bottom',
  showLabel = true,
  ...props
}) => {
  const iconToShow = active && tab.activeIcon ? tab.activeIcon : tab.icon;

  return (
    <TabBarItem
      active={active}
      disabled={tab.disabled}
      variant={variant}
      size={size}
      onPress={tab.disabled ? undefined : onPress}
      {...props}
    >
      {active && variant !== 'segmented' && (
        <ActiveIndicator variant={variant} />
      )}
      
      {variant === 'segmented' && active && (
        <ActiveIndicator variant={variant} />
      )}
      
      {iconToShow && (
        <TabIcon size={size}>
          {iconToShow}
          {tab.badge && (
            <TabBadge>
              <BadgeText>
                {typeof tab.badge === 'number' && tab.badge > 99 ? '99+' : tab.badge}
              </BadgeText>
            </TabBadge>
          )}
        </TabIcon>
      )}
      
      {showLabel && (
        <TabLabel active={active} size={size}>
          {tab.title}
        </TabLabel>
      )}
    </TabBarItem>
  );
};

// 主标签栏组件
export const TabBar: React.FC<TabBarProps> = ({
  tabs,
  activeKey,
  defaultActiveKey,
  onTabChange,
  variant = 'bottom',
  size = 'medium',
  safeArea = true,
  showLabels = true,
  ...props
}) => {
  const [internalActiveKey, setInternalActiveKey] = useState(
    activeKey || defaultActiveKey || tabs[0]?.key
  );
  
  const currentActiveKey = activeKey || internalActiveKey;
  const insets = useSafeAreaInsets();
  
  const handleTabPress = (key: string) => {
    if (activeKey === undefined) {
      setInternalActiveKey(key);
    }
    onTabChange?.(key);
  };

  const safeAreaPadding = safeArea && variant === 'bottom' 
    ? { paddingBottom: insets.bottom } 
    : {};

  return (
    <TabBarContainer
      variant={variant}
      size={size}
      style={safeAreaPadding}
      {...props}
    >
      {tabs.map((tab) => (
        <TabBarItemComponent
          key={tab.key}
          tab={tab}
          active={currentActiveKey === tab.key}
          onPress={() => handleTabPress(tab.key)}
          size={size}
          variant={variant}
          showLabel={showLabels}
        />
      ))}
    </TabBarContainer>
  );
};
