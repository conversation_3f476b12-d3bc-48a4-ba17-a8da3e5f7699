/**
 * 列表组件
 * 提供统一的列表布局和列表项样式
 */

import React from 'react';
import { YStack, XStack, Text, View, styled, GetProps } from 'tamagui';

// 列表组件属性
export interface ListProps extends GetProps<typeof YStack> {
  data?: any[];
  renderItem?: (item: any, index: number) => React.ReactNode;
  separator?: boolean;
  variant?: 'default' | 'inset' | 'grouped';
  size?: 'small' | 'medium' | 'large';
}

// 列表项组件属性
export interface ListItemProps extends GetProps<typeof XStack> {
  title?: string;
  subtitle?: string;
  description?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  rightContent?: React.ReactNode;
  pressable?: boolean;
  onPress?: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'inset';
}

// 样式化列表容器
const ListContainer = styled(YStack, {
  name: 'ListContainer',
  
  variants: {
    variant: {
      default: {
        backgroundColor: '$background',
      },
      
      inset: {
        backgroundColor: 'transparent',
        marginHorizontal: '$4',
        borderRadius: '$4',
        overflow: 'hidden',
      },
      
      grouped: {
        backgroundColor: 'transparent',
        gap: '$3',
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'default',
  },
});

const ListItemContainer = styled(XStack, {
  name: 'ListItemContainer',
  alignItems: 'center',
  backgroundColor: '$background',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  
  variants: {
    pressable: {
      true: {
        cursor: 'pointer',
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
        },
      },
    },
    
    size: {
      small: {
        paddingHorizontal: '$3',
        paddingVertical: '$2',
        minHeight: 44,
      },
      
      medium: {
        paddingHorizontal: '$4',
        paddingVertical: '$3',
        minHeight: 56,
      },
      
      large: {
        paddingHorizontal: '$5',
        paddingVertical: '$4',
        minHeight: 64,
      },
    },
    
    variant: {
      default: {
        // 默认样式
      },
      
      inset: {
        marginHorizontal: '$4',
        borderRadius: '$3',
        marginBottom: '$1',
        borderBottomWidth: 0,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
    variant: 'default',
  },
});

const ListItemLeft = styled(XStack, {
  name: 'ListItemLeft',
  alignItems: 'center',
  
  variants: {
    size: {
      small: {
        marginRight: '$2',
      },
      
      medium: {
        marginRight: '$3',
      },
      
      large: {
        marginRight: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ListItemContent = styled(YStack, {
  name: 'ListItemContent',
  flex: 1,
  justifyContent: 'center',
});

const ListItemRight = styled(XStack, {
  name: 'ListItemRight',
  alignItems: 'center',
  
  variants: {
    size: {
      small: {
        marginLeft: '$2',
      },
      
      medium: {
        marginLeft: '$3',
      },
      
      large: {
        marginLeft: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ListItemTitle = styled(Text, {
  name: 'ListItemTitle',
  fontWeight: '500',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ListItemSubtitle = styled(Text, {
  name: 'ListItemSubtitle',
  color: '$placeholderColor',
  marginTop: '$1',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ListItemDescription = styled(Text, {
  name: 'ListItemDescription',
  color: '$placeholderColor',
  marginTop: '$1',
  lineHeight: '$3',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ListSeparator = styled(View, {
  name: 'ListSeparator',
  height: 1,
  backgroundColor: '$borderColor',
  marginLeft: '$4',
});

// 列表项组件
export const ListItem: React.FC<ListItemProps> = ({
  title,
  subtitle,
  description,
  leftIcon,
  rightIcon,
  rightContent,
  pressable = false,
  onPress,
  size = 'medium',
  variant = 'default',
  ...props
}) => {
  return (
    <ListItemContainer
      pressable={pressable}
      onPress={pressable ? onPress : undefined}
      size={size}
      variant={variant}
      {...props}
    >
      {leftIcon && (
        <ListItemLeft size={size}>
          {leftIcon}
        </ListItemLeft>
      )}
      
      <ListItemContent>
        {title && (
          <ListItemTitle size={size}>
            {title}
          </ListItemTitle>
        )}
        {subtitle && (
          <ListItemSubtitle size={size}>
            {subtitle}
          </ListItemSubtitle>
        )}
        {description && (
          <ListItemDescription size={size}>
            {description}
          </ListItemDescription>
        )}
      </ListItemContent>
      
      {(rightContent || rightIcon) && (
        <ListItemRight size={size}>
          {rightContent || rightIcon}
        </ListItemRight>
      )}
    </ListItemContainer>
  );
};

// 主列表组件
export const List: React.FC<ListProps> = ({
  children,
  data,
  renderItem,
  separator = true,
  variant = 'default',
  size = 'medium',
  ...props
}) => {
  return (
    <ListContainer variant={variant} {...props}>
      {data && renderItem ? (
        data.map((item, index) => (
          <React.Fragment key={index}>
            {renderItem(item, index)}
            {separator && index < data.length - 1 && <ListSeparator />}
          </React.Fragment>
        ))
      ) : (
        React.Children.map(children, (child, index) => (
          <React.Fragment key={index}>
            {child}
            {separator && index < React.Children.count(children) - 1 && <ListSeparator />}
          </React.Fragment>
        ))
      )}
    </ListContainer>
  );
};
