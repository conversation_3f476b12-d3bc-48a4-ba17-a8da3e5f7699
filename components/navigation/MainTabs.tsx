/**
 * 主应用底部Tab导航组件
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MainTabParamList } from '@/types/navigation';
import { TAB_CONFIGS, TAB_BAR_CONFIG } from '@/constants/navigation';
import { TabIcon } from './TabIcon';

const Tab = createBottomTabNavigator<MainTabParamList>();

// 临时占位组件，后续替换为实际页面组件
const ResidentsScreen = () => null;
const OrganizationScreen = () => null;
const AccessScreen = () => null;
const StatisticsScreen = () => null;
const LogsScreen = () => null;
const NoticesScreen = () => null;
const PhonesScreen = () => null;
const ProfileScreen = () => null;

// 组件映射
const SCREEN_COMPONENTS = {
  Residents: ResidentsScreen,
  Organization: OrganizationScreen,
  Access: AccessScreen,
  Statistics: StatisticsScreen,
  Logs: LogsScreen,
  Notices: NoticesScreen,
  Phones: PhonesScreen,
  Profile: ProfileScreen,
};

export const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: TAB_BAR_CONFIG.activeTintColor,
        tabBarInactiveTintColor: TAB_BAR_CONFIG.inactiveTintColor,
        tabBarStyle: TAB_BAR_CONFIG.style,
        tabBarLabelStyle: TAB_BAR_CONFIG.labelStyle,
        tabBarIconStyle: TAB_BAR_CONFIG.iconStyle,
      }}
    >
      {TAB_CONFIGS.map((config) => (
        <Tab.Screen
          key={config.name}
          name={config.name}
          component={SCREEN_COMPONENTS[config.name]}
          options={{
            title: config.title,
            tabBarIcon: ({ focused, color, size }) => (
              <TabIcon
                name={config.icon}
                focused={focused}
                color={color}
                size={size}
              />
            ),
          }}
        />
      ))}
    </Tab.Navigator>
  );
};
