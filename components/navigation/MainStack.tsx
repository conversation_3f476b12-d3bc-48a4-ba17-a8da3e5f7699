/**
 * 主应用栈导航组件
 */

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MainStackParamList } from '@/types/navigation';
import { MainTabs } from './MainTabs';

// 临时占位组件，后续替换为实际页面组件
const AddResidentScreen = () => null;
const EditResidentScreen = () => null;
const ResidentDetailScreen = () => null;
const AddStaffScreen = () => null;
const EditStaffScreen = () => null;
const AddPositionScreen = () => null;
const EditPositionScreen = () => null;
const SetPasswordScreen = () => null;
const RecordCardScreen = () => null;
const RecordFaceScreen = () => null;
const CameraScreen = () => null;
const PublishNoticeScreen = () => null;
const NoticeDetailScreen = () => null;
const AddPhoneScreen = () => null;
const EditPhoneScreen = () => null;
const SettingsScreen = () => null;

const Stack = createNativeStackNavigator<MainStackParamList>();

export const MainStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
        animation: 'slide_from_right',
        headerBackTitle: '返回',
      }}
    >
      {/* 主Tab导航 */}
      <Stack.Screen 
        name="MainTabs" 
        component={MainTabs}
        options={{
          headerShown: false,
        }}
      />
      
      {/* 住户管理子页面 */}
      <Stack.Screen 
        name="AddResident" 
        component={AddResidentScreen}
        options={{
          title: '新增住户',
        }}
      />
      <Stack.Screen 
        name="EditResident" 
        component={EditResidentScreen}
        options={{
          title: '编辑住户',
        }}
      />
      <Stack.Screen 
        name="ResidentDetail" 
        component={ResidentDetailScreen}
        options={{
          title: '住户详情',
        }}
      />
      
      {/* 组织发展子页面 */}
      <Stack.Screen 
        name="AddStaff" 
        component={AddStaffScreen}
        options={{
          title: '新增员工',
        }}
      />
      <Stack.Screen 
        name="EditStaff" 
        component={EditStaffScreen}
        options={{
          title: '编辑员工',
        }}
      />
      <Stack.Screen 
        name="AddPosition" 
        component={AddPositionScreen}
        options={{
          title: '新增岗位',
        }}
      />
      <Stack.Screen 
        name="EditPosition" 
        component={EditPositionScreen}
        options={{
          title: '编辑岗位',
        }}
      />
      
      {/* 门禁管理子页面 */}
      <Stack.Screen 
        name="SetPassword" 
        component={SetPasswordScreen}
        options={{
          title: '设置密码',
        }}
      />
      <Stack.Screen 
        name="RecordCard" 
        component={RecordCardScreen}
        options={{
          title: '录入门卡',
        }}
      />
      <Stack.Screen 
        name="RecordFace" 
        component={RecordFaceScreen}
        options={{
          title: '录入人像',
        }}
      />
      <Stack.Screen 
        name="Camera" 
        component={CameraScreen}
        options={{
          title: '拍照',
          headerShown: false,
        }}
      />
      
      {/* 通知公告子页面 */}
      <Stack.Screen 
        name="PublishNotice" 
        component={PublishNoticeScreen}
        options={{
          title: '发布通知',
        }}
      />
      <Stack.Screen 
        name="NoticeDetail" 
        component={NoticeDetailScreen}
        options={{
          title: '通知详情',
        }}
      />
      
      {/* 电话管理子页面 */}
      <Stack.Screen 
        name="AddPhone" 
        component={AddPhoneScreen}
        options={{
          title: '添加电话',
        }}
      />
      <Stack.Screen 
        name="EditPhone" 
        component={EditPhoneScreen}
        options={{
          title: '编辑电话',
        }}
      />
      
      {/* 设置子页面 */}
      <Stack.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: '设置',
        }}
      />
    </Stack.Navigator>
  );
};
