/**
 * 认证栈导航组件
 */

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/types/navigation';

// 临时占位组件，后续替换为实际页面组件
const LoginScreen = () => null;
const BindAdminScreen = () => null;
const BindSuccessScreen = () => null;

const Stack = createNativeStackNavigator<AuthStackParamList>();

export const AuthStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen 
        name="Login" 
        component={LoginScreen}
        options={{
          title: '登录',
        }}
      />
      <Stack.Screen 
        name="BindAdmin" 
        component={BindAdminScreen}
        options={{
          title: '绑定管理员',
          headerShown: true,
          headerBackTitle: '返回',
        }}
      />
      <Stack.Screen 
        name="BindSuccess" 
        component={BindSuccessScreen}
        options={{
          title: '绑定成功',
          headerShown: true,
          headerBackVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};
