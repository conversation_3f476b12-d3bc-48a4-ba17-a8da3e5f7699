/**
 * Tab图标组件
 */

import React from 'react';
import { View } from 'react-native';
import { TabIconName } from '@/types/navigation';

interface TabIconProps {
  name: TabIconName;
  focused: boolean;
  color: string;
  size: number;
}

// 图标映射 - 临时使用简单的View，后续替换为实际图标
const ICON_MAP: Record<TabIconName, string> = {
  users: '👥',
  organization: '🏢',
  key: '🔑',
  'chart-bar': '📊',
  history: '📋',
  bell: '🔔',
  phone: '📞',
  user: '👤',
};

export const TabIcon: React.FC<TabIconProps> = ({ 
  name, 
  focused, 
  color, 
  size 
}) => {
  return (
    <View
      style={{
        width: size,
        height: size,
        justifyContent: 'center',
        alignItems: 'center',
        opacity: focused ? 1 : 0.6,
      }}
    >
      {/* 临时使用emoji，后续替换为vector icons */}
      <View
        style={{
          fontSize: size * 0.8,
          color,
        }}
      >
        {/* 这里后续会替换为实际的图标组件 */}
      </View>
    </View>
  );
};
