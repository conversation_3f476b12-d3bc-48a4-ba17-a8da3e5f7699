/**
 * 管理员绑定页面
 * 对应UC-001: 首次绑定管理员账号
 */

import { router } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button } from '@/components/ui/Button/Button';
import { Captcha } from '@/components/ui/Captcha';
import { Input } from '@/components/ui/Input/Input';
import { theme } from '@/config/theme';
import { mockBindAdmin, mockSendVerificationCode } from '@/data/authMockData';
import { useAuthStore } from '@/stores/auth';
import { BindAdminData } from '@/types/auth';

export default function BindAdminScreen() {
  const { bindAdmin } = useAuthStore();
  
  // 表单数据
  const [formData, setFormData] = useState<BindAdminData>({
    dynamicCode: '',
    password: '',
    name: '',
    phone: '',
    verificationCode: '',
    captcha: '',
  });

  // 表单错误
  const [errors, setErrors] = useState<Partial<BindAdminData>>({});
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 输入框引用
  const passwordRef = useRef<any>(null);
  const nameRef = useRef<any>(null);
  const phoneRef = useRef<any>(null);
  const codeRef = useRef<any>(null);

  // 倒计时定时器
  const countdownRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // 更新表单数据
  const updateFormData = (field: keyof BindAdminData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Partial<BindAdminData> = {};

    if (!formData.dynamicCode.trim()) {
      newErrors.dynamicCode = '请输入动态口令';
    } else if (formData.dynamicCode.length !== 6) {
      newErrors.dynamicCode = '动态口令应为6位数字';
    }

    if (!formData.password.trim()) {
      newErrors.password = '请输入口令密码';
    }

    if (!formData.name.trim()) {
      newErrors.name = '请输入姓名';
    } else if (formData.name.length < 2) {
      newErrors.name = '姓名至少2个字符';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入正确的手机号';
    }

    if (!formData.verificationCode.trim()) {
      newErrors.verificationCode = '请输入验证码';
    } else if (formData.verificationCode.length !== 4) {
      newErrors.verificationCode = '验证码应为4位数字';
    }

    if (!formData.captcha?.trim()) {
      newErrors.captcha = '请输入图形验证码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!formData.phone.trim()) {
      setErrors(prev => ({ ...prev, phone: '请先输入手机号' }));
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入正确的手机号' }));
      return;
    }

    setSendingCode(true);
    try {
      const result = await mockSendVerificationCode(formData.phone);
      if (result.success) {
        Alert.alert('提示', result.message);
        startCountdown();
      } else {
        Alert.alert('发送失败', '验证码发送失败，请稍后重试');
      }
    } catch (error) {
      Alert.alert('发送失败', '网络错误，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  };

  // 开始倒计时
  const startCountdown = () => {
    setCountdown(60);
    countdownRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          if (countdownRef.current) {
            clearInterval(countdownRef.current);
            countdownRef.current = null;
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 提交绑定
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const result = await mockBindAdmin(formData);
      
      if (result.success && result.data) {
        // 保存认证信息到Store
        await bindAdmin(result.data.user, result.data.token, result.data.refreshToken);
        
        Alert.alert(
          '绑定成功',
          '管理员账号绑定成功！',
          [
            {
              text: '确定',
              onPress: () => {
                // 跳转到主界面
                router.replace('/(tabs)');
              },
            },
          ]
        );
      } else {
        Alert.alert('绑定失败', result.error?.message || '绑定失败，请重试');
      }
    } catch (error) {
      Alert.alert('绑定失败', '网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 清理定时器
  React.useEffect(() => {
    return () => {
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* 头部 */}
          <View style={styles.header}>
            <Text style={styles.title}>绑定管理员账号</Text>
            <Text style={styles.subtitle}>
              首次使用需要绑定管理员账号
            </Text>
          </View>

          {/* 表单 */}
          <View style={styles.form}>
            {/* 动态口令 */}
            <Input
              label="动态口令"
              placeholder="请输入6位动态口令"
              keyboardType="numeric"
              value={formData.dynamicCode}
              onChangeText={(text) => updateFormData('dynamicCode', text)}
              error={!!errors.dynamicCode}
              errorText={errors.dynamicCode}
              maxLength={6}
              returnKeyType="next"
              onSubmitEditing={() => passwordRef.current?.focus()}
              required
            />

            {/* 口令密码 */}
            <Input
              ref={passwordRef}
              label="口令密码"
              placeholder="请输入口令密码"
              secureTextEntry
              showPasswordToggle
              value={formData.password}
              onChangeText={(text) => updateFormData('password', text)}
              error={!!errors.password}
              errorText={errors.password}
              returnKeyType="next"
              onSubmitEditing={() => nameRef.current?.focus()}
              required
            />

            {/* 姓名 */}
            <Input
              ref={nameRef}
              label="姓名"
              placeholder="请输入您的姓名"
              value={formData.name}
              onChangeText={(text) => updateFormData('name', text)}
              error={!!errors.name}
              errorText={errors.name}
              returnKeyType="next"
              onSubmitEditing={() => phoneRef.current?.focus()}
              required
            />

            {/* 手机号 */}
            <Input
              ref={phoneRef}
              label="手机号"
              placeholder="请输入手机号"
              keyboardType="phone-pad"
              value={formData.phone}
              onChangeText={(text) => updateFormData('phone', text)}
              error={!!errors.phone}
              errorText={errors.phone}
              maxLength={11}
              returnKeyType="next"
              onSubmitEditing={() => codeRef.current?.focus()}
              required
            />

            {/* 短信验证码 */}
            <Input
              ref={codeRef}
              label="短信验证码"
              placeholder="请输入4位验证码"
              keyboardType="numeric"
              value={formData.verificationCode}
              onChangeText={(text) => updateFormData('verificationCode', text)}
              error={!!errors.verificationCode}
              errorText={errors.verificationCode}
              maxLength={4}
              rightIcon={
                <Button
                  variant="outline"
                  size="small"
                  loading={sendingCode}
                  disabled={countdown > 0}
                  onPress={handleSendCode}
                >
                  {countdown > 0 ? `${countdown}s` : '发送验证码'}
                </Button>
              }
              required
            />

            {/* 图形验证码 */}
            <Captcha
              value={formData.captcha}
              onChangeText={(text) => updateFormData('captcha', text)}
              error={errors.captcha}
            />
          </View>

          {/* 提交按钮 */}
          <View style={styles.footer}>
            <Button
              variant="primary"
              size="large"
              fullWidth
              loading={loading}
              onPress={handleSubmit}
            >
              绑定管理员账号
            </Button>
            
            <Text style={styles.hint}>
              绑定后您将成为该小区的管理员，可以管理住户信息、员工信息等
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  keyboardView: {
    flex: 1,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.pageHorizontal,
    paddingVertical: theme.spacing.pageVertical,
  },

  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
    paddingTop: theme.spacing.lg,
  },

  title: {
    fontSize: theme.typography.h2.fontSize,
    fontWeight: theme.typography.h2.fontWeight as any,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },

  subtitle: {
    fontSize: theme.typography.body2.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: theme.typography.body2.lineHeight,
  },

  form: {
    flex: 1,
    marginBottom: theme.spacing.xl,
  },

  footer: {
    paddingTop: theme.spacing.lg,
  },

  hint: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: theme.spacing.md,
    lineHeight: theme.typography.caption.lineHeight,
    paddingHorizontal: theme.spacing.md,
  },
});
