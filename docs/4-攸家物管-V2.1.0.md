# 产品需求文档 (PRD): 攸家物管 App V2.1.0

## 版本历史

| 版本号 | 日期       | 作者   | 变更说明                         |
| :----- | :--------- | :----- | :------------------------------- |
| V2.1.0    | 2024-04-27 | 曹文娟 |   门禁系统相关的功能         |

## 1. 引言 (Introduction)

### 1.1 文档目的

本产品需求文档（PRD）旨在详细定义 **攸家物管App** 的功能需求、非功能需求、用户场景及相关细节。本文档是设计、开发、测试和验收攸家物管 App 的主要依据。

### 1.2 产品概述

**门禁管理系统** 是一个利用物联网(IoT)、人工智能(AI)、大数据等技术打造的智慧化居住环境综合平台。 **攸家物管App**作为该系统的重要组成部分，是专为小区物业管理人员设计的移动工作端。它赋能物业员工（管家、保安、保洁、维修工、绿化工等）通过手机高效处理核心管理任务，包括：管理住户信息与门禁权限、管理内部员工与岗位、管理个人通行凭证、信息统计、日志查看、社区内容发布（物业通知、寻人寻物、失物招领、闲置物品）、常用电话管理等，旨在提升物业管理效率、降低运营成本、优化服务质量。

### 1.3 产品目标

*   **业务目标:**
    * 提高物业对住户信息及门禁权限管理的效率和准确性。
    * 规范化物业内部组织架构和员工权限管理。
    * 提升物业通知的发布效率和住户触达率。
    * 为物业提供便捷、移动化的管理工具，支持现场办公。
*  **用户目标 (物业人员):**
    * 随时随地、方便快捷地完成住户登记、信息修改、凭证录入等操作。
    * 清晰地管理团队岗位、员工及其工作权限。
    * 高效发布各类社区通知和重要公告。
    * 轻松管理个人工作所需的门禁通行权限。
    * 快速查询住户登记情况和个人操作记录。
    
### 1.4 目标用户

* **核心用户群体: 小区物业管理人员**
    * **角色细分:**
        * **管理员 (Admin):** 通常由物业经理或指定负责人担任，拥有 App 内所有模块的管理权限。其账号通过关联【攸家运管】Web 平台生成的动态口令进行首次绑定激活。同一小区仅能有一个管理员账号。
        * **普通员工 (Staff):** 由管理员或其他授权员工（如：管家）在 App 内添加，根据被分配的岗位获得相应的菜单和操作权限。系统预设了“管家”、“保安”、“保洁”、“维修工”、“绿化工”等岗位及其基础权限，也可自定义岗位。
    * **特征:** 需要在小区内进行大量的移动作业，涉及与住户的直接交互、内部团队管理、信息发布等。对 App操作的便捷性、信息准确性、权限安全性有较高要求。
    * **需求/目标:** 高效完成日常管理任务，减少重复性工作，提高响应速度，确保信息同步，保障小区安全。

### 1.5 范围

**本次版本包含 :**
*   用户登录与注册（基于手机号）
*   App 自动更新提示与升级
*   住户信息管理（列表、查询、添加、认证、编辑、删除 ）
*   员工信息管理（列表、查询、添加、编辑、删除）
*   岗位管理（列表、添加、编辑 删除、权限分配）
*   门禁管理（设置密码 、录入门卡 、人像录入）
*   登记统计（列表、查询（查看各个户室登记住户的情况））
*   门禁日志（列表、按日期筛选查询）
*   通知公告（列表、发布、撤回、详情查看、编辑、删除）
*   电话管理（常用电话列表、添加、删除、编辑）
*   我的小区信息查看
*   个人中心（修改昵称、修改头像、修改手机号）
*   基础设置（关于我们、注销账户、退出登录）


### 1.6 术语定义

*   **攸家物管:** 本 App 的名称。
*   **物业:** 指小区管理处。
*   **住户:** 居住在小区内的业主或租户。
*   **员工:** 物业公司的管理人员、安保、客服等。
*   **岗位:** 为员工定义的角色，关联不同操作权限。
*   **门禁:** 小区的出入口控制设备。
*   **认证:** 对住户身份信息的审核确认过程。
*   **UI:** 用户界面 (User Interface)。

## 2. 用户故事 / Use Cases - 攸家物管

**UC-001: 首次绑定管理员账号**
*   **用户故事:** 作为一个首次使用【攸家物管】App 的物业管理员，我想要使用【攸家运管】提供的动态口令、图形验证码和短信验证码，将我的手机号与管理员账号绑定，以便激活我的管理员权限并开始使用 App。
*   **参与者:** 攸家物管 App 管理员 (首次使用)
*   **前置条件:** 已在【攸家运管】注册并生成动态口令；管理员已获得口令；App 已安装。
*   **基本流程:**
    1.  用户首次打开 App，系统展示Logo/启动页后，自动检测到用户未登录，跳转至“登录”页面。
    2.  用户点击“绑定管理员”按钮。
    3.  系统跳转至“绑定管理员账号”页面（包含：动态口令输入框、口令密码输入框姓名输入框、手机输入框号、短信验证码输入框、确定绑定按钮）。
    4.  用户输入【攸家运管】提供的“动态口令”。
    5.  用户输入【攸家运管】提供的“口令密码”。
    6.  用户输入管理员的“姓名”。
    7.  用户输入接收短信验证码的“手机号码”。
    8.  用户点击“获取验证码”按钮。
    9.  向目标手机号发送短信验证码，同时“获取验证码”按钮变为倒计时状态。
    10.  用户查收短信，并将收到的“短信验证码”输入对应框内。
    11. 用户点击页面底部的“确认绑定”按钮。
    12. 系统校验所有信息通过，执行后台绑定操作。
    13. 绑定成功，系统跳转至“绑定成功”提示页面（包含：管理员姓名、管理员手机号、提示语-使用管理员手机号登录并使用【攸家物管】、我知道了并登录按钮）。
    14. 用户点击“我知道了并登录”按钮。
    15. 系统跳转到登录页面，完成登录，触发UC-002。
*   **异常/替代流程:**
    *   **E1: 动态口令错误:** 提交时提示“动态口令错误或无效”。
    *   **E2: 动态口令已过期:** 提交时提示“动态口令已过期，请联系客服重新生成”。
    *   **E3: 口令密码错误:** 提交时提示“口令密码错误”
    *   **E4: 口令密码过期:** 提交时提示“口令密码已过期，请联系客服重新生成”。
    *   **E5: 获取短信验证码失败 (网络/短信网关错误):** 点击按钮提示“短信验证码发送失败，请稍后重试”。
    *   **E6: 获取短信验证码频繁:** 短时间内多次点击（10分钟内可发5次，达到5次后限制1分钟后方可继续发送），提示“操作过于频繁，请稍后再试”。
    *   **E7: 短信验证码输入错误:** 点击“确认绑定”时提示“短信验证码错误”。
    *   **E8: 短信验证码已过期 (5分钟):** 点击“确认绑定”时提示“短信验证码已失效，请重新获取”。
    *   **E9: 手机号已被绑定:** 提示“该手机号已注册为员工账号，无法绑定为管理员”。
    *   **E10: 网络连接失败/超时:** 在获取短信验证码或提交绑定请求时提示“网络连接失败，请检查网络后重试”。
    *   **E11: 服务器内部错误:** 提交绑定请求后提示“绑定失败，服务异常，请联系技术支持”。
    *   **E12: 小区已存在管理员:** 提交绑定时最新绑定的管理员覆盖之前绑定的管理员。
    
**UC-002: 物业员工登录 App**
*   **用户故事:** 作为一个物业员工，我想要通过手机号和验证码安全地登录攸家物管 App，以便开始使用 App 进行日常工作。
*   **参与者:** 物业员工
*   **前置条件:** 用户已安装攸家物管 App。
*   **基本流程:**
    1.  用户打开 App，进入登录页面。
    2.  用户输入已注册的手机号码。
    3.  用户点击“获取验证码”按钮。
    4.  系统向该手机号发送短信验证码，按钮变为倒计时状态 (倒计时时长60 秒)。
    5.  用户输入收到的短信验证码。
    6.  用户点击“登录”按钮。
    7.  系统校验验证码。
    8.  验证通过，用户成功登录并进入 App 主界面（住户管理）
*   **异常/替代流程:**
    *   **E1: 手机号为空/格式错误:** 用户没有输入手机号或用户输入的手机号长度不等于11，点击“获取验证码”或“登录”时，输入框下方提示“请输入手机号”或“请输入正确的手机号码”。
    *   **E2: 手机号未注册/无权限:** 用户输入的手机号未在系统中注册或已被禁用，点击“登录”时，提示“账号不存在或无权限登录”。
    *   **E3:验证码为空/格式错误:** 用户没有输入验证码或用户输入的验证码长度不等于4，点击“登录”时，输入框下方提示“请输入验证码”或“请输入正确格式的验证码”。
    *   **E4: 获取验证码失败:** 因短信通道问题或网络问题导致验证码发送失败，提示“验证码发送失败，请稍后重试”。
    *   **E5: 获取验证码过于频繁:** 用户在短时间内多次点击“获取验证码”，达到频率限制时，提示“操作频繁，请稍后再试”。
    *   **E6: 验证码错误/过期:** 用户输入的验证码不正确或已超过有效期 ，点击“登录”时，提示“验证码错误或已失效”。
    *   **E7: 网络连接失败:** 设备无网络连接或服务器连接中断，点击“获取验证码”或“登录”时，提示“网络连接失败，请检查网络设置”。
    *   **E8: 服务器内部错误:** 服务器处理登录请求时发生未知错误，提示“登录失败，请稍后重试”或通用错误提示。
    
**UC-003: App 检测到新版本并提示升级**
*   **用户故事:** 作为一个 App 用户，我想要在 App 启动时自动检测是否有新版本，并能选择是否立即更新，以便使用最新的功能和修复。
*   **参与者:** 所有 App 用户
*   **前置条件:** 用户已启动 App，且设备已连接网络。
*   **基本流程:**
    1.  App 启动后，自动向服务器发送请求，检查是否有新版本。
    2.  服务器返回版本信息。
    3.  如果当前版本为最新，则无任何提示，流程结束。
    4.  如果检测到新版本，弹窗提示用户。弹窗内容包括：新版本号 (如 V1.2.0)、更新内容描述、可选升级类型（强制/建议）、“稍后升级”和“立即更新”按钮。
    5.  用户点击“立即更新”。
    6.  App 开始下载更新包，弹窗内显示下载进度条。
    7.  下载完成后，根据操作系统引导用户进行安装。
    8.  安装完成后，App 自动重启或提示用户手动重启。
*   **可选流程:**
    *   用户选择“稍后升级”，弹窗关闭，本次启动不再提示。
    *   强制升级：弹窗仅提供“立即更新”按钮，用户不升级则无法继续使用 App
*   **异常/替代流程:**
    *   **E1: 检测更新失败:** 因网络问题或服务器问题导致无法检查新版本，不弹出提示框或提示“检查更新失败”。
    *   **E2: 用户选择下次再说:** 用户点击“下次再说”，关闭提示框，App 正常运行。下次启动时仍会提示。
    *   **E3: 下载过程中网络中断:** 提示“下载中断，请检查网络后重试”，并暂停下载。网络恢复后可继续下载 
    *   **E4: 下载失败:** 因服务器、网络或其他原因导致下载失败，提示“下载失败，请重试”。
    *   **E5: 存储空间不足:** 下载完成准备安装前，检测到设备存储空间不足，提示“存储空间不足，请清理后重试”。
    *   **E6: 用户取消下载:** 用户在下载过程中点击取消，停止下载。
    *   **E7: 安装失败:** 用户确认安装后，因系统原因（如解析包错误、签名不一致等）安装失败，由操作系统层面处理，App 可能无法直接干预。
    *   **E8: 强制更新:** 如果服务器配置为强制更新，则更新提示框 可能没有“下次再说”按钮，用户必须点击“立即升级”才能继续使用 App。

**UC-004: 管理员查看和管理住户信息**
*   **用户故事:** 作为一个物业管理员，我想要查看不同认证状态（已认证、待认证、未通过）的住户列表，进行搜索和筛选，并对选中的住户执行审批、信息修改、门禁权限管理（录入门卡/人像、下发数据）和解绑等操作。
*   **参与者:** 物业管理员/客服
*   **前置条件:** 用户已成功登录 App，并拥有住户管理权限。
*   **基本流程:**
    1.  用户进入“住户管理”页面。页面顶部显示用户头像、昵称、当前小区名称（可切换）、退出和设置按钮。下方是主要功能 Tab（住户管理、组织发展、门禁管理、登记统计、门禁日志、通知公告、电话管理）。
    2.  “住户管理” Tab 默认选中。下方显示搜索框（提示“请输入住户手机号或住户姓名”）和“清空”按钮。
    3.  搜索框下方是状态 Tab：“已认证 (数量)”、“待认证 (数量)”、“未通过 (数量)”，以及一个“筛选”按钮 (按楼栋/单元/楼层/户室)。默认选中一个 Tab（如“已认证”）。
    4.  **列表展示 (根据 Tab 不同):**
        *   **已认证:**
            *   列表项包含：头像占位符、姓名、认证通过图标和时间戳、房产信息（图标+楼栋单元房号）、联系电话（图标+脱敏手机号）、业主/租户标签 、登记方式、门禁密码状态、人像状态、门卡列表（卡号 + [删除] 按钮）。
            *   每个列表项右侧有一个选择框（单选）。
        *   **待认证:**
            *   列表项包含：头像占位符、姓名、申请时间戳、房产信息、联系电话。
            *   每个列表项右侧有一个选择框。
        *   **未通过:**
            *   列表项包含：头像占位符、姓名、申请时间戳、房产信息、联系电话。
            *   下方显示“未通过理由: [具体原因]”。
            *   每个列表项右侧有一个选择框。
    5.  用户可以点击不同的状态 Tab 切换列表内容。
    6.  用户可以在搜索框输入关键词，实时筛选列表。点击“清空”清除搜索内容。
    7.  用户点击“筛选”按钮，弹出筛选对话框（楼栋/单元/楼层/户室），确认后列表根据筛选结果刷新。
    8.  **列表项交互:**
        *   点击列表项右侧的选择框，选中该住户（单选）。
    9.  **底部操作栏 (根据所选 Tab 和选中状态出现):**
        *   **选中“待认证”住户时:** 
            *   底部出现“未通过”、“通过”按钮。
            *   点击“通过”：二次确认后，系统更新该住户状态为“已认证”，刷新列表。
            *   点击“未通过”：弹出驳回原因填写/选择框，确认后更新状态为“未通过”，刷新列表。
        *   **选中“已认证”住户时:** 
            * 底部出现“解绑”、“修改”、“录入门卡”、“录入人像”、“下发数据”按钮。
            *   点击“解绑”：弹出确认提示，确认后执行解绑操作（解绑住户与户室关系），刷新列表。
            *   点击“修改”：跳转到住户信息编辑页面。（除手机号无法修改，其他在添加时填写的信息均可修改）。
            *   点击“录入门卡”：跳转到门卡录入流程 (参考 UC-008)。
            *   点击“录入人像”：跳转到人像录入流程 (参考 UC-006)。
            *   点击“下发数据”：弹出“下发数据”弹窗。用户选择目标门禁设备（支持多选/全选），点击“确定下发”，系统将该住户的密码、NFC、人像信息同步到所选设备。
        *   **选中“未通过”住户时:** 
            *   底部无任何操作按钮
    10. 用户可以点击单个门卡后的“[删除]”按钮，可删除该门卡 ( 需确认交互和是否有确认提示)。
    11. 用户可以点击右下角的“+”按钮，进入新增住户流程 (UC-005)。
    12. 列表支持下拉刷新。
*   **异常/替代流程:**
    *   **E1: 列表加载失败:** 进入页面或切换 Tab/筛选/刷新时，因网络或服务器问题加载数据失败，显示错误提示（如“加载失败，请稍后重试”）和重试按钮。
    *   **E2: 列表为空:** 某个状态或筛选条件下没有住户数据，显示空状态提示（如“暂无待认证住户”）。
    *   **E3: 搜索无结果:** 用户输入搜索关键词后，没有匹配的住户，列表区域显示“未找到相关住户”。
    *   **E4: 认证操作失败:** 点击“通过”或“驳回”后，因网络或服务器问题导致操作失败，提示“操作失败，请重试”。
    *   **E5: 进入修改页失败:** 点击“修改”按钮时，加载数据失败或跳转失败，提示错误。
    *   **E6: 网络错误:** 在进行任何需要网络请求的操作（加载、刷新、搜索、认证、解绑、下发等）时发生网络错误，给出相应提示。
    *   **E7: 解绑操作失败:** 点击“确定解绑”后，操作失败，提示“解绑失败，请重试”。
    *   **E8: 删除门卡失败:** 点击门卡后的“删除”按钮后，操作失败，提示“删除失败，请重试”。
    *   **E9: 下发数据失败 - 未选择设备:** 点击“确定下发”时未选择任何门禁设备，提示“请选择要下发的门禁设备”。
    *   **E10: 下发数据失败 - 同步错误:** 下发过程中，与部分或全部设备同步失败，提示“部分设备下发失败，请重试”。
    *   **E11: 解绑、修改、录入门卡、录入人像、下发数据时未选择住户:** 点击对应按钮，提示“请先选择想要操作的住户”。
    
**UC-005: 管理员新增住户信息**
*   **用户故事:** 作为一个物业管理员，我想要通过验证住户手机号、输入姓名并选择一个或多个房产及其关系（业主/租户），来手动登记新住户，并在登记成功后可以选择立即为其录入人像或门卡。
*   **参与者:** 物业管理员/客服
*   **前置条件:** 用户已成功登录 App，位于“住户管理”页面，并拥有住户管理的权限。
*   **基本流程:**
    1.  在“住户管理”页面，用户点击右下角的“+”按钮。
    2.  系统跳转至“登记住户”页面。
    3.  用户输入住户的“手机号”。
    4.  用户点击“获取验证码”按钮。**[替代流 E2]**
    5.  系统校验手机号格式通过后，向该手机号发送短信验证码，同时“获取验证码”按钮变为倒计时状态。
    6.  用户输入收到的“验证码”。
    7.  用户输入住户“姓名”。
    8.  用户点击“+ 选择户室”按钮。
    9.  系统弹出房产选择器，显示当前小区的楼栋/单元/楼层/房号。
    10. 用户在选择器中选择具体的楼栋、单元、楼层、房号。
    11. 用户点击选择器中的“确定”按钮。
    12. 选择的房产信息（如“1号楼1单元302”）被添加到“住址”区域，旁边默认显示“业主”和“租户”单选按钮 (默认都不选中)，以及一个删除图标 (垃圾桶)。
    13. 用户为该房产选择关系：“业主”或“租户”。
    14. (可选) 用户重复步骤 8-13，为该住户添加更多房产信息。
    15. (可选) 用户点击某个已添加房产旁的删除图标，移除该条房产信息。
    16. 用户点击页面底部的“确定登记”按钮。 **[异常流 E1、 E2、E8]**
    17. 系统进行信息校验（验证码是否正确/有效）。
    18.系统根据所选房产的登记状态，弹出“提示”框，说明该住户将成为“第一登记人”或“家庭成员”，并提示登记成功后可使用智慧小区功能。用户需在此提示框点击“确定登记”进行最终确认。**[替代流 E10]**
    19. 系统后台执行住户信息保存操作，并将“登记方式”标记为“物业登记”。**[异常流 E7]**
    20. 登记成功，系统弹出成功提示框，显示住户姓名和“已登记成功，请为他录入人像或门卡。”信息，并提供“取消”、“录入门卡”、“录入人像”按钮。
    21. **后续操作选择:**
        *   用户点击“录入门卡”: 系统跳转至门卡录入流程 (参考 UC-007)，并将当前住户信息传递过去。
        *   用户点击“录入人像”: 系统跳转至人像录入流程 (参考 UC-006)，并将当前住户信息传递过去 。
        *   用户点击“取消”或提示框右上角的“X”: 关闭成功提示框，系统返回“住户管理”列表页面并选中“已认证”Tab。
*   **异常/替代流程:**
    *   **E1: 必填项为空:** 点击“确定登记”时，若手机号、验证码、姓名未填写，或未添加任何住址信息，或添加了住址但未选择关系，提示“请输入手机号”、“请输入验证码”、“请输入住户姓名”、“请选择户室”、“请选择住户关系”等。
    *   **E2: 手机号格式错误:** 输入手机号后，点击“获取验证码”或“确定登记”时，若格式不正确(长度不等于11)，提示“请输入正确的手机号码”。
    *   **E3: 获取短信验证码失败 (网络/短信网关错误):** 点击“获取验证码”按钮无反应或提示“短信验证码发送失败，请稍后重试”。
    *   **E4: 获取短信验证码频繁:** 短时间内多次点击“获取验证码”，提示“操作过于频繁，请稍后再试”。
    *   **E5: 验证码错误/过期:** 点击“确定登记”时，系统校验验证码不正确或已失效，提示“验证码错误或已失效”
    *   **E6: 户室选择器加载失败:** 点击“+ 选择户室”时，加载楼栋/单元/房号数据失败，选择器为空或提示错误。
    *   **E7: 保存失败 (网络/服务器错误):** 点击“确定登记”后，因网络或服务器问题导致后台保存失败，提示“登记失败，请检查网络连接”或“服务器错误，请稍后重试”。
    *   **E8: 用户取消登记:** 用户在“登记住户”页面点击设备或导航栏的返回按钮，系统应提示“是否放弃本次编辑？” ，确认后返回住户管理列表。
    *   **E9: 用户取消选择户室:** 在户室选择器弹窗中，用户点击“取消”，弹窗关闭，不添加新的户室信息。
    *   **E10: 用户取消登记 (在确认提示框):** “提示”框中，用户点击“取消”，提示框关闭，停留在“登记住户”页面，允许用户修改信息。

**UC-006: 录入人像信息 (用于门禁)**
*   **用户故事:** 作为一个负责门禁的管理员，我想要为选定的住户录入、选定的员工、重新录入人像照片，以用于门禁系统的人脸识别。或当前用户想要为自己重新录入人像，以用于门禁系统的人脸识别。
*   **参与者:** 物业管理员/安保人员
*   **前置条件:**
    *   用户已成功登录 App。
*   **基本流程:**
    1.   用户已在住户列表选中了某个住户或用户在员工列表选中了某个员工或用户已进入门禁管理页面。
    2.  用户点击“录入人像”按钮。
    3.  系统检查并请求摄像头权限（如果尚未授权）。 **[异常流 E1]**
    4.  系统跳转至“拍照”界面。界面顶部显示当前操作的“住户姓名”。
    5.  界面显示摄像头实时预览，并有圆形引导框和提示文字“拍照时请保证人像置于提示框内”，以及拍照要点提示（正对手机、光线充足、避免遮挡）。
    6.  用户调整好位置和角度，点击屏幕下方的圆形“快门”按钮进行拍照。
    7.  系统捕获照片，并跳转到照片确认界面。界面同样显示“住户姓名”和拍摄的照片。
    8.  照片下方有“重新拍照”按钮和注意事项说明（如：人像仅用于门禁、需本人真实照片、审核后可用等）。
    9.  用户确认照片符合要求，点击底部的“确定录入”按钮。 
    10. 系统执行人像质量检测（如：是否包含多人脸、是否没有人脸）。**[异常流 E2]**
    11. 系统上传人像照片到服务器，并与对应用户关联。**[异常流 E3、E5]**
    12. 上传成功，系统提示“人像录入成功” 最新人像覆盖之前的人像，并将最新人像数据下发到当前用户所关联的门禁设备。
    13. 系统自动清空拍照页面、清空当前页面。
    14. 对应页面上的人像照片、人像状态更新。
*   **替代流程:**
    *   **A1: 重新拍照 (从确认页):** 在照片确认界面 ，用户觉得照片不满意，点击“重新拍照”按钮，系统返回拍照界面 ，用户可以重新拍摄。
    *   **A2: 取消录入 (从拍照页):** 用户在拍照界面，点击左上角的返回箭头，系统应提示“是否放弃本次录入？” ，确认后返回上一页面。
    *   **A3: 取消录入 (从确认页):** 用户在照片确认界面 ，点击左上角的返回箭头，系统返回拍照界面 ，用户可以重新拍摄。
*   **异常/替代流程:**
    *   **E1: 未授予摄像头权限:** 系统请求权限时用户拒绝，提示“无法访问摄像头，请在系统设置中开启权限”，停留在当前页面或返回详情页。
    *   **E2: 人脸检测失败/照片质量不合格 (后端检测):** 点击“确定录入”时，系统检测到无人脸，提示“未能清晰识别人脸，请重新拍摄”或检测到多张人像，提示“识别到多张人像，请重新拍摄”，并停留在确认界面。
    *   **E3: 上传失败 (网络/服务器错误):** 点击“确定录入”后上传过程中失败，提示“上传失败，请检查网络连接”或“服务器错误，请稍后重试”。
    *   **E5: 状态更新失败:** 人像上传成功，但在详情页状态未能及时更新，用户可能需要手动下拉刷新页面。
    

**UC-007: 录入/删除门卡信息 (用于门禁)**
*    **用户故事:** 作为一个负责门禁的管理员，我想要为选定的住户录入、选定的员工、重新录入门卡，以用于门禁系统。或当前用户想要为自己重新录入门卡，以用于门禁系统。
*   **参与者:** 物业管理员/安保人员
*   **前置条件:**
    *   用户已成功登录 App。
    *   用户的手机具备 NFC 功能并已开启。
    *   用户已在住户列表选中了某个住户或用户在员工列表选中了某个员工或用户已进入门禁管理页面。
*   **基本流程 1: 录入门卡 **
    1.  用户点击“录入门卡”按钮。
    2.  系统检查 NFC 功能是否开启。 **[异常流 E1]**
    3.  系统弹出“录入门卡”模态框，提示“请将门卡贴于手机背面 NFC 感应区”，并显示等待读取的动画或占位图。
    4.  用户将实体门卡靠近手机的 NFC 感应区域。
    5.  手机 NFC 读取到卡片信息 (通常是卡片的 UID)。**[异常流 E2]**
    7.  系统在模态框内给出读取成功的反馈（如图标变化、提示文字变化 “读取成功”）。
    8.  系统将读取到的卡片信息与该住户关联并保存到服务器。**[异常流 E3、E4、E5]**
    9.  保存成功，系统提示“门卡录入成功”，并将NFC数据下发到当前用户所关联的门禁设备。
    10. 模态框自动关闭。
    11. 对应页面上的门卡信息、门卡状态更新。

*   **基本流程 2: 删除门卡**
    1.   用户已在住户列表选中了某个住户或用户在员工列表选中了某个员工或用户已进入门禁管理页面。
    2.  用户点击“[删除]”按钮。
    3.  系统弹出确认提示框：“确定要删除门卡吗？”。
    4.  用户点击“确定”。
    5.  系统从服务器解除该门卡与用户的关联。**[异常流 E6]**
    6.  删除成功，系统提示“门卡已删除”,并将NFC数据从与当前用户所关联的门禁设备移除。
    7. 对应页面上的门卡信息、门卡状态更新。

*   **异常/替代流程:**
    *   **E1: NFC 未开启或设备不支持:** 点击“录入门卡”时，检测到 NFC 关闭或不支持，提示“请先在系统设置中开启 NFC 功能”或“当前设备不支持 NFC”。
    *   **E2: 用户取消录入:** 在“录入门卡”模态框  中，用户点击右上角的“X”，关闭模态框。
    *   **E3: 不支持的卡片类型:** 读取到的卡片非系统支持的类型 ，提示“不支持的卡片类型”。
    *   **E4: 门卡已被其他住户注册:** 读取到的卡片信息已在系统中关联了其他住户，提示“该门卡已被 [其他住户姓名/手机号] 注册，无法重复录入”。
    *   **E5: 保存失败 (网络/服务器错误):** 卡片读取成功后，向服务器保存数据时失败，提示“门卡保存失败，请检查网络或稍后重试”。
    *   **E6: 删除失败 (网络/服务器错误):** 点击确认删除后，操作失败，提示“门卡删除失败，请重试”。

**UC-008: 设置密码信息（用于门禁）**
*    **用户故事:** 作为一个负责门禁的管理员，我想要选定的员工、重新设置密码，以用于门禁系统。或当前用户想要为自己重新设置密码，以用于门禁系统。
*   **参与者:** 物业管理员/安保人员
*   **前置条件:**
    *   用户已成功登录 App。
    *   用户在员工列表选中了某个员工或用户已进入门禁管理页面。
*   **基本流程:**
    1.  用户点击“[设置密码]”按钮。
    2.  系统弹出“设置密码”模态框。
    3.  模态框内包含 6 个密码输入框，下方有提示文字：“1、请勿输入相同的数字，如：666666。”、“2、请勿输入相连的数字，如：123456。”，以及“取消”、“清空”、“确定”按钮。
    4.  用户输入密码（不需要隐藏）。
    5.  用户点击“确定”按钮。 **[异常流 E1, E2]**
    6.  系统校验输入的密码：
        *   是否为 6 位数字。
        *   是否符合安全规则（非 6 位相同数字，非 6 位连续数字）。
    7.  校验通过后，系统将密码与该住户关联并保存到服务器。**[异常流 E3]**
    8.  保存成功，系统提示“密码设置成功” ，并将密码下发到当前用户所关联的门禁设备。
    9.  “设置密码”模态框关闭。
    10. 对应页面上的密码状态更新。

*   **替代流程:**
    *   **A1: 清空输入:** 用户在输入过程中或输入完成后，点击“清空”按钮，所有 6 个密码输入框恢复为空。
    *   **A2: 取消操作:** 用户点击“取消”按钮或模态框右上角的“X”，关闭“设置密码”模态框，不保存任何更改。
*   **异常/替代流程:**
    *   **E1: 输入位数不足:** 用户点击“确定”时，输入的数字少于 6 位，系统提示“请输入 6 位密码”，停留在模态框。
    *   **E2: 密码不符合规则:** 用户点击“确定”时，输入的 6 位密码是 6 位相同数字或 6 位连续数字，系统提示“密码过于简单，请勿使用相同或连续数字”，停留在模态框。
    *   **E3: 保存失败 (网络/服务器错误):** 点击“确定”后，因网络或服务器问题导致后台保存失败，提示“密码设置失败，请检查网络连接”或“服务器错误，请稍后重试”，停留在模态框。


**UC-009: 管理员管理组织架构 (员工与岗位)**
*   **用户故事:**
    * 员工：作为一个组织管理员，我想要添加、查看、修改和删除员工信息，并管理他们的岗位、门禁权限和楼栋权限，以便进行人员管理和访问控制。
    * 岗位：作为一个组织管理员，我想要创建、查看、修改和删除岗位，并定义每个岗位的功能权限，以便进行基于角色的权限控制。
*   **参与者:** 具备组织管理权限的管理员 (如物业经理)
*   **前置条件:**
    *   用户已成功登录 App，并拥有“组织发展”模块的管理权限。
    *   系统已配置了小区/楼栋/单元的门禁点信息。
    
**A. 员工管理 **

*   **A.1 查看员工列表**
    1.  用户进入 App主界面，点击“组织发展”Tab。
    2.  系统默认显示“员工管理”子标签页。
    3.  界面展示员工列表，每行显示员工人像照片、姓名、岗位、部分手机号、注册时间戳、员工的门禁凭证状态（门禁密码、人像、门卡：未录入/已录入）和门禁权限。
    5.  用户可以滚动列表查看更多员工。
    6.  用户可以在顶部的搜索框输入员工手机号或姓名进行搜索。
    7.  用户可以点击列表项右侧的单选按钮进行选择。

*   **A.2 添加新员工**
    1.  在“员工管理”列表页，用户点击右下角的“+”浮动按钮。
    2.  系统跳转到“登记员工”页面 。
    3.  用户输入员工的手机号（*必填）。
    4.  用户点击“获取验证码”。系统向该手机号发送验证码短信。
    5.  用户输入收到的验证码（*必填）。
    6.  用户输入员工的姓名（*必填）。
    7.  用户点击“+选择岗位”。系统弹出“选择岗位”模态框，显示可用岗位列表。
    8.  用户选择一个或多个岗位 ，点击“确定”。选择的岗位显示在“登记员工”页面的岗位区域，可点击红色减号删除。
    9.  用户点击“+选择门禁”。系统弹出“选择门禁权限”模态框，显示小区/楼栋/单元结构。
    10. 用户勾选该员工可通行的门禁点（如单元门），可使用“全选”等快捷操作，点击“确定”。选择的门禁权限显示在“登记员工”页面，可点击红色减号删除。
    11. 用户点击“+选择楼栋”，系统弹出“选择楼栋权限”模态框，显示小区/楼栋/单元结构。
    12. 用户选择该员工可管理的楼栋范围，点击“确定”。选择的楼栋显示在页面，可点击红色减号删除。
    13. 用户填写完所有必填信息后，点击底部的“确定登记”按钮。
    14. 系统进行表单校验（手机号格式、验证码是否正确、姓名非空等）。
    15. 系统弹出确认提示框：“确认登记【姓名】为工作人员？”并提示登录方式。
    16. 用户点击“确定登记”。
    17. 系统将员工信息、岗位关联、权限信息保存到服务器。
    18. 保存成功，系统弹出成功提示框，显示员工姓名和头像占位符，提示“已登记成功”，并提供快捷操作按钮：“取消”、“设置密码”、“录入门卡”、“录入人像”。
    19. 用户可以点击快捷按钮进行后续操作（跳转至 UC-008密码, UC-007门卡, UC-006人像），或点击“取消”关闭提示框。
    20. 系统返回“员工管理”列表页，新添加的员工显示在列表中。

*   **A.3 修改员工信息**
    1.  在“员工管理”列表页，用户选择一个员工（点击列表项选中）。
    2.  用户点击底部的“修改”按钮。
    3.  系统跳转到类似“登记员工”的编辑页面，预填该员工的现有信息（姓名、岗位、门禁权限、楼栋权限）。手机号不可修改。验证码字段不显示。
    4.  用户修改姓名、增删岗位、增删门禁权限、增删楼栋权限。
    5.  用户点击“确定修改” 按钮。
    6.  系统进行校验并保存更改。
    7.  保存成功，系统提示“修改成功”并返回员工列表页。

*   **A.4 删除员工**
    1.  在“员工管理”列表页，用户选择一个员工。
    2.  用户点击底部的“删除”按钮。
    3.  系统弹出确认提示框：“确定要删除选中的员工吗？删除后将无法恢复。”
    4.  用户点击“确定”。
    5.  系统从服务器删除员工记录及其关联信息（不删除其操作日志）。
    6.  删除成功，系统提示“删除成功”并刷新员工列表。

*   **A.5 管理员工门禁凭证 (快捷入口)**
    1.  在“员工管理”列表页 ，用户选择一个员工。
    2.  用户点击底部的“设置密码”、“录入门卡”或“录入人像”按钮。
    3.  系统跳转到对应的功能页面 (参考 UC-008密码, UC-007门卡, UC-006人像)。

*   **A.6 下发员工数据 (快捷入口)**
    1.  在“员工管理”列表页，用户选择一个员工。
    2.  用户点击底部的“下发数据”按钮：弹出“下发数据”弹窗。用户选择目标门禁设备（支持多选/全选），点击“确定下发”，系统将该员工的密码、NFC、人像信息同步到所选设备。

**B. 岗位管理**

*   **B.1 查看岗位列表**
    1.  用户进入“组织发展”模块，点击顶部的“岗位管理”子标签页。
    2.  系统显示已创建和内置的岗位列表，每行显示岗位名称和该岗位拥有的主要功能权限摘要。
    3.  用户可以滚动列表查看更多岗位。
    4.  用户可以在顶部的搜索框输入岗位名称进行搜索。
    5.  用户可以点击列表项右侧的勾选框进行选择（用于批量操作）。

*   **B.2 添加新岗位**
    1.  在“岗位管理”列表页，用户点击右下角的“+”浮动按钮。
    2.  系统跳转到“添加岗位”页面 。
    3.  用户输入岗位名称（*必填）。
    4.  用户点击“+选择功能”。系统弹出“功能权限”模态框，显示可分配的系统功能模块（住户管理、组织发展、门禁管理、登记统计、门禁日志、通知公告、电话管理）。
    6.  用户选择该岗位应拥有的一个或多个功能权限，点击“确定”。选择的权限显示在“添加岗位”页面，可点击红色减号移除。
    7.  用户填写完所有必填信息后，点击底部的“确定添加”按钮。
    8.  系统进行表单校验（名称非空等）。
    9.  系统将新岗位及其功能权限关联保存到服务器。
    10. 保存成功，系统提示“岗位添加成功”并返回岗位列表页 ，新岗位显示在列表中。

*   **B.3 修改岗位信息**
    1.  在“岗位管理”列表页，用户选择一个岗位。
    2.  用户点击底部的“修改”按钮。
    3.  系统跳转到类似“添加岗位” 的编辑页面，预填该岗位的现有信息（名称、功能权限）。
    4.  用户修改岗位名称、增删功能权限。
    5.  用户点击“确定修改” 按钮。
    6.  系统进行校验并保存更改。
    7.  保存成功，系统提示“修改成功”并返回岗位列表页。

*   **B.4 删除岗位**
    1.  在“岗位管理”列表页，用户选择一个或多个岗位。
    2.  用户点击底部的“删除”按钮。
    3.  系统弹出确认提示框“确定要删除选中的岗位吗？删除后将无法恢复。” 
    4.  用户点击“确定”。
    5.  系统从服务器删除岗位记录。
    6.  删除成功，系统提示“删除成功”并刷新岗位列表，与之关联的员工数据自动解绑。

*   **B.5 系统内置岗位 **
    1.  系统内置“管家”岗位，功能权限包含：住户管理、组织发展、通知公告、登记统计、门禁日志、门禁管理、电话管理。
    2.  系统内置“保安”岗位，功能权限包含：门禁管理。
    3.  系统内置“保洁”岗位，功能权限包含：门禁管理。
    4.  系统内置“维修”岗位，功能权限包含：门禁管理。

*   **异常/替代流程 :**
    *   **E1: 手机号格式错误/已被注册:** 添加员工时，输入的手机号格式不正确或已被系统其他员工注册，提示错误。
    *   **E2: 验证码错误/过期:** 输入的验证码不正确或已超时失效，提示错误。
    *   **E3: 必填项为空:** 未填写姓名等必填字段，点击确定时提示“请填写[字段名]”。
    *   **E4: 服务器错误:** 保存员工/岗位信息时，后端服务异常，提示“操作失败，请稍后重试”。
    *   **E5: 网络连接问题:** 提交数据时网络中断，提示“网络连接失败，请检查网络设置”。
    *   **E6: 获取验证码失败:** 因短信通道、手机号问题等导致无法发送验证码，提示“验证码发送失败，请重试”。
    *   **E7: 未选择岗位:** 用户未选择岗位就提交，提示“请选择岗位”。
    *   **E8: 修改员工信息失败:** 保存修改后的员工信息时出错。
    *   **E9: 删除员工信息失败:** 删除员工时出错（如网络问题）。
    *   **E10: 岗位名称已存在:** 添加岗位时，输入的名称与现有岗位重复，提示“岗位名称已存在”。
    *   **E11: 添加岗位失败:** 保存新岗位时出错。
    *   **E12: 修改岗位失败:** 保存修改后的岗位信息时出错。
    *   **E13: 删除岗位失败:** 删除岗位时出错。

**UC-010:门禁管理-用户管理自己的人像、门卡、密码**
*   **用户故事:** 作为一个物业工组人员，我想管理自己的人像、门卡、密码，并且能够方便地发起对这些凭证的设置、录入或删除操作，以及触发数据下发。
*   **参与者:** 攸家物管用户
*   **前置条件:**
    *   用户已成功登录 App，并拥有门禁管理权限。
*   **基本流程 (界面展示与入口):**
    1.  用户进入 App主界面，点击“门禁管理”Tab。
    2.  页面顶部当前登录用户的基本信息（如姓名、身份/岗位、联系方式等）。
    3.  页面关键区域显示用户的门禁凭证状态：
        *   **门禁密码:** 显示状态，如“未设置”或“已设置”。
        *   **人像:** 显示状态，如“未录入”或“已录入”；显示人像照片。
        *   **门卡:** 显示状态，如“未录入”或“已录入”。如果“已录入”，显示门卡旁边显示的“删除”按钮 。 （可录入多张）
    4.  页面展示该用户当前拥有的 **门禁权限** 列表（如“1号楼1单元、2号楼1单元、北门...”）。此列表通常在此页面为只读状态。
    5.  页面底部固定显示操作栏，包含以下按钮：
        *   **设置密码:** 用于为该用户设置或修改门禁密码，触发UC-008。
        *   **录入门卡:** 用于为该用户录入新的门禁卡，触发UC-007。
        *   **录入人像:** 用于为该用户录入人脸信息，触发UC-006。
        *   **下发数据:** 弹出“下发数据”弹窗。用户选择目标门禁设备（支持多选/全选），点击“确定下发”，系统将该用户的密码、NFC、人像信息同步到所选设备。。

*   **异常/替代流程:**
    *   **E1: 进入凭证管理流程失败:** 点击“设置密码”、“录入门卡”、“录入人像”时，因系统问题或缺少必要设备（如未连接发卡器）导致无法进入相应流程，系统应给出提示。
   
**UC-011:管理员查看用户凭证登记状态统计**

*   **用户故事:** 作为一个管理员，我想要按楼栋/单元/户室查看住户的门禁凭证（人像、门卡、密码）的登记状态，以便了解凭证录入的整体情况和找到未完成登记的用户。
*   **参与者:** 物业工作人员 (具备相应权限)
*   **前置条件:**
    *   用户已成功登录 App，并拥有登记统计的权限。
    *   系统中已存在楼栋/单元/户室及住户信息 (部分或全部)。
*   **基本流程:**
    1.  用户进入 App主界面，点击“登记统计”Tab。
    2.  系统默认显示所有第一页户室的住户凭证登记状态概览。信息按户室分组（如“1栋1单元102室”）。
    3.  每个户室下：
        *   如果户室有已登记住户，则显示住户列表（卡片形式）。
        *   每个住户卡片上显示姓名，并通过图标（如人脸、卡片、密码）和状态指示（如绿色/灰色）清晰展示该住户对应凭证（人像、门卡、密码）是否已登记/录入。
        *   如果户室下“暂未登记任何住户信息”，则显示相应提示。
    4.  用户可以通过顶部的筛选控件按“楼栋”、“单元”或“户室”进行筛选，以查看特定范围内的登记信息。
    5.  用户可以点击“清空”按钮清除筛选条件，恢复显示全部信息。
    6.  用户可以滚动列表查看更多户室和住户的登记状态。
*   **替代/异常流程:**
    *   **E1:** 筛选后无匹配结果：系统显示“未找到符合条件的记录”或类似提示。
    *   **E2:** 无任何住户/户室信息：系统显示“暂无数据”或引导用户去添加住户。
    *   **E3:** 加载失败：因网络或服务器问题无法加载数据，系统应提示用户检查网络或稍后重试。


**UC-012: 管理员查看门禁系统操作日志**

*   **用户故事:** 作为一个管理员，我想要查看门禁管理系统的操作日志，了解谁在什么时间执行了哪些操作（如数据下发、人员信息修改、凭证录入/删除等）及其结果，以便进行审计追踪或问题排查。
*   **参与者:** 物业工作人员 (具备相应权限)
*   **前置条件:**
    *   用户已成功登录 App，并拥有门禁日志权限。
    *   系统已记录了物业工作人员的操作行为。
*   **基本流程:**
    1.  用户进入 App主界面，点击“门禁日志”Tab。
    2.  系统默认按时间倒序显示最近的操作日志列表。
    3.  每条日志包含关键信息：
        *   操作时间 (年/月/日 时:分:秒)
        *   操作人 (执行操作的管理员账号/姓名)
        *   操作模块 (登记住户、修改住户、下发数据、解绑住户、录入人像、录入门卡、设置密码等模块)
        *   操作结果 (成功/失败)
        *   被操作对象/人 (可选，指明操作影响的住户/员工)
    4.  用户可以通过顶部的筛选控件按“日期范围”进行筛选 。
    5.  用户在日期选择器中单个日期，点击“确定”。
    6.  系统根据选择的日期过滤并显示相应的日志记录。
    7.  用户可以点击“清空”按钮清除日期筛选条件，恢复显示全部日志。
    8.  用户可以滚动列表加载和查看更多历史日志。
*   **替代/异常流程:**
    *   **E1:** 筛选后无匹配日志：系统显示“该日期范围内无操作日志”或类似提示。
    *   **E2:** 无任何日志记录：系统显示“暂无操作日志”。
    *   **E3:** 加载失败：因网络或服务器问题无法加载日志，系统应提示用户检查网络或稍后重试。


**UC-013: 发布和管理通知公告**

*   **用户故事:** 作为一个管理员，我想要发布新的通知公告（如物业通知、寻物招领、寻人寻物、闲置物品等）给指定范围的住户，并能对已发布的通知进行查看、筛选、修改、撤回、删除、重新发布以及管理留言权限，以便有效传达信息和维护公告内容。
*   **参与者:** 物业工作人员(具备通知管理权限)
*   **前置条件:**
    *   已成功登录 App。
    *   拥有通知公告的权限。
    *   系统中已存在小区/楼栋/单元/住户的基础信息。
*   **基本流程:**

    **A. 查看与筛选通知列表**
    1.  用户进入 App主界面，点击“通知公告”Tab。
    2.  系统默认按发布时间倒序显示通知公告列表。
    3.  列表项展示关键信息：发布状态 (“已发布/已撤回”)、分类标签 (如【物业通知】、【寻人寻物】)、标题/内容摘要、发布者、发布时间、评论数
    4.  管理员可以点击顶部筛选区域，按“时间范围”筛选通知。 
    5.  管理员可以点击“清空”按钮清除筛选条件，恢复显示默认列表。
    6.  管理员可以向下滚动加载更多历史通知。
    **B. 发布新通知 **
    1.  在通知列表页 ，管理员点击右下角的悬浮“+”按钮。
    2.  系统导航至“发布通知”页面 。
    3.  管理员首先选择通知的分类标签 (如“寻人寻物”、“失物招领”、“闲置物品”、“物业通知”等)。
    4.  管理员在内容区域输入通知的正文。
    5.  管理员点击图片区域的“+”号，从设备选择并上传一张或多张图片或一个视频（30以内） (可对已上传图片/视频进行删除)。
    6.  管理员点击“物业通知标题”栏，进入标题设置页 ，可以选择预设模板并手动输入标题，点击“确定”返回。
    7.  管理员点击“发布者名称”栏，进入名称设置页，输入发布者名称 (如“某某物业中心”)，点击“确定”返回。
    8.  管理员点击“接收住户”栏，进入接收对象选择页：
        *   可以选择按楼栋/单元/楼层/户室等范围指定 (如：显示的“1栋”、“1栋1单元”)。
    9.  管理员根据需要，通过开关控件设置是否“允许留言”。
    10. 管理员检查所有信息无误后，点击页面底部的“发布”按钮。
    11. 系统进行必要的校验 (如标题、内容是否为空)，校验通过后保存通知，向接收住户推送 ，并通常返回到通知列表页，新发布的通知显示在列表顶部。
    **C. 查看通知详情与评论 **
    1.  在通知列表页，管理员点击任意一条通知。
    2.  系统导航至该通知的详情页面 。
    3.  页面显示通知的完整标题、通知时间、正文内容、图片、发布者。
    4.  如果该通知允许留言，下方会显示留言列表，包括留言人信息、内容、时间、回复和点赞按钮。管理员可以查看所有留言。
    5.  (可选) 管理员可以在评论区对住户留言进行回复。
    **D. 管理已发布通知 **
    1.  管理员可以在通知列表页选择一条通知，或者在通知详情页找到底部的操作栏。
    2.  管理员根据需要点击相应的管理按钮：
        *   **撤回:** 将通知状态变为已撤回，住户端通常不再可见。系统二次需要确认操作。
        *   **删除:** 软删除该条通知。系统统二次需要确认操作。
        *   **修改:** 进入类似“发布通知”的编辑页面，预填当前通知的所有信息。管理员修改后点击““发布”更新通知。
        *   **重新发布:** (用于已撤回的通知) 使通知再次对目标住户可见。系统二次需要确认操作。
        *   **允许留言/禁止留言:** 点击切换该通知是否允许住户留言。按钮状态会相应改变。
        *   **允许私信/禁止私信:**  点击切换是否允许住户就此通知私信发布者。

*   **替代/异常流程:**
    *   **E1:** 发布通知时校验失败 (如标题为空、未选择接收对象等)：系统应提示具体错误信息，停留在发布页面。
    *   **E2:** 图片/视频上传失败：系统应提示上传失败，允许用户重试或移除失败的图/视频片。
    *   **E3:** 网络或服务器错误：在加载列表、查看详情、发布或执行管理操作时，系统应提示网络错误，建议用户检查网络或稍后重试。
    *   **E4:** 筛选后无结果：系统在列表区域显示“暂无符合条件的通知”或类似提示。
    *   **E5:** 删除/撤回/重新发布确认时用户取消：操作不执行，停留在当前页面。


**UC-014: 管理常用电话**

*   **用户故事:** 作为一个管理员，我想要添加、查看、修改和删除小区常用的服务电话（如物业客服、维修、安保等），并能直接从应用内发起拨号，以便快速联系相关方，同时也方便让住户在他们的应用端查看这些公共联系方式。
*   **参与者:** 物业工作人员 (具备电话管理权限)
*   **前置条件:**
    *   已成功登录 App。
    *   拥有电话管理的权限。
*   **基本流程:**
    **A. 查看常用电话列表 **
    1.  用户进入 App主界面，点击“电话管理”Tab。
    2.  系统显示已添加的常用电话列表。
    3.  每个列表项显示：部门名称、电话号码、备注信息（如工作时间）。
    4.  列表项右侧可能有一个选择控件，用于标记要操作的条目。

    **B. 添加新电话 **
    1.  在电话管理列表页 ，管理员点击右下角的悬浮“+”按钮。
    2.  系统导航至“添加电话”页面 。
    3.  管理员在“部门名称”字段输入名称 (必填)。
    4.  管理员在“固定电话/手机号”字段输入号码 (必填)。
    5.  管理员在“备注”字段输入额外信息 (可选，如工作时间)。
    6.  管理员点击页面底部的“确定添加”按钮。
    7.  系统校验输入的必填项是否完整，以及电话号码格式（可选校验）。
    8.  校验通过后，系统保存新的电话信息。
    9.  系统返回到电话管理列表页，新添加的电话显示在列表中。
    10. (系统提示: 底部文字所示) 告知管理员添加成功后，住户可通过指定途径（如“攸优”App）查看此号码。
    **C. 修改现有电话**
    1.  在电话管理列表页，管理员选择一个需要修改的电话条目。
    2.  管理员点击页面底部的“修改”按钮。
    3.  系统导航至类似“添加电话”的编辑页面，但页面标题为“修改电话”，并预填了所选电话的现有信息。
    4.  管理员修改部门名称、电话号码或备注信息。
    5.  管理员点击页面底部的“确定修改”按钮。
    6.  系统进行必要的校验。
    7.  校验通过后，系统更新电话信息。
    8.  系统返回到电话管理列表页，列表显示更新后的信息。
    **D. 删除电话 (截图 1)**
    1.  在电话管理列表页，选择一个需要删除的电话条目。
    2.  管理员点击页面底部的“删除”按钮。
    3.  系统弹出确认对话框（例如：“确定要删除选中的电话吗？”）。
    4.  管理员确认删除。
    5.  系统从数据库中移除选中的电话信息。
    6.  系统刷新电话管理列表页，被删除的条目不再显示。
    **E. 直接拨号 **
    1.  在电话管理列表页，管理员选择一个需要拨打的电话条目。
    2.  管理员点击页面底部的“直接拨号”按钮。
    3.  系统调用设备的拨号功能，并自动填入所选的电话号码，提示用户发起呼叫。

*   **替代/异常流程:**
    *   **E1:** 添加/修改时，必填项为空或格式错误：系统在添加/修改页面提示错误信息，阻止提交，要求用户修正。
    *   **E2:** 用户尝试修改/删除/拨号，但未选择任何条目：系统提示用户“请先选择需要操作的电话号码”。
    *   **E3:** 用户在删除确认对话框中选择“取消”：删除操作被中止，列表保持不变。
    *   **E4:** 网络或服务器错误：在加载列表、添加、修改、删除操作时，系统提示“操作失败，请检查网络或稍后重试”。
    *   **E5:** 设备无拨号功能或权限不足：系统调用拨号功能失败，可能由操作系统层面处理，或App内提示“无法拨打电话”。
    *   **E6:** 列表为空：首次进入或删除所有条目后，列表区域显示“暂无常用电话”或类似提示，引导用户添加。

**UC-015: 设置-访问和使用设置功能**
*   **用户故事:** 作为一个 攸家物管App 用户，我想要访问设置页面，执行如查看“关于我们”、检查更新（手动入口）、或者注销账户等操作。
*   **参与者:** 攸家物管App 用户
*   **前置条件:** 用户已登录 App，并进入“设置”页面。
*   **基本流程:**
    1.  用户进入“设置”页面。
    2.  页面显示设置项列表：
        *   关于我们：点击后进入“关于我们”页面，显示 App Logo、版本号、服务协议、隐私政策入口。
        *   检查更新：点击后手动触发 UC-003 的检查更新流程。
        *   注销账户：点击后进入注销账户说明/申请页面，告知注销后果和流程。
    3.  用户点击底部“退出登录”按钮，弹出确认框，确认后返回到登录页面 (UC-002)。
*   **异常流程:**
    *   访问服务协议/隐私政策失败 (网络错误)：提示加载失败。

**UC-016:我的- 查看与管理个人信息**
* **用户故事:** 作为一个【攸家物管】App用户，我想要能方便地查看我的个人资料，包括头像、姓名、手机号和昵称，并且能够更换我的头像、修改姓名和昵称，以及在需要时修改我的登录手机号（通过验证旧手机号）或安全退出登录。
* **参与者:** 攸家物管 App 用户 (所有已登录用户)
* **前置条件:** 用户已登录【攸家物管】App。
* **基本流程:**
    1.  用户在App主界面点击“我的头像”图标。
    2.  系统进入“我的”页面。
    3.  **【查看信息】** 页面顶部显示用户当前的头像，下方列表显示用户的姓名、手机号（部分隐藏）、昵称。
    4.  **【更换头像】**
        * 用户点击头像图片或“更换头像”文字/按钮。
        * App 弹出操作菜单，提供“拍照”和“从相册选择”选项。**[异常/替代流程 P1.1]**
        * 用户选择其中一种方式获取新图片。
        * App 提供图片裁剪功能。用户调整裁剪框后确认。**[异常/替代流程 P1.2]**
        * App 上传裁剪后的图片到服务器。**[异常/替代流程 P1.3]**
        * 上传成功后，个人信息页面的头像更新。
    5.  **【修改姓名】**
        * 用户点击“姓名”所在行（显示当前姓名和 ">" 图标）。
        * App 跳转到“修改姓名”页面或弹出输入框，预填当前姓名。
        * 用户输入新姓名后点击“保存”或“完成”。**[异常/替代流程 P2]**
        * 保存成功后，App 返回个人信息页面，姓名更新。
    6.  **【修改昵称】**
        * 用户点击“昵称”所在行（显示当前昵称或“暂无”和 ">" 图标）。
        * App 跳转到“修改昵称”页面或弹出输入框。
        * 用户输入新昵称后点击“保存”或“完成”。**[异常/替代流程 P3]**
        * 保存成功后，App 返回个人信息页面，昵称更新。
    7.  **【修改手机号】**
        * 用户点击“手机号”所在行（显示部分隐藏的手机号和 ">" 图标）。
        * App 进入修改手机号流程：
            * (a) 验证旧手机号（发送验证码到旧号，用户输入验证）。**[异常/替代流程 P4.1]**
            * (b) 输入新手机号。
            * (c) 验证新手机号（发送验证码到新号，用户输入验证）。**[异常/替代流程 P4.2, P4.3]**
            * (d) 确认修改。**[异常/替代流程 P4.4]**
        * 修改成功后，App 可能提示重新登录或返回个人信息页面，手机号显示更新。
    8.  **【退出登录】**
        * 用户在个人信息页面（或通过顶部的“设置”入口进入的设置页面）找到并点击“退出登录”按钮。
        * App 弹出确认退出对话框。
        * 用户确认退出。**[异常/替代流程 P5]**
        * App 清除本地登录状态（Token），返回到登录页面 (UC-002)。
* **异常/替代流程:**
    * **P1: 更换头像失败**
        * **P1.1: 获取图片失败:** App 无相机/相册权限；用户取消拍照/选择；读取相册失败。
        * **P1.2: 图片裁剪失败:** 裁剪工具出错；用户取消裁剪。
        * **P1.3: 图片上传失败:** 网络错误；图片过大/格式不支持；服务器处理失败。
    * **P2: 修改姓名失败**
        * **P2.1: 前端校验失败:** 姓名为空；姓名过长或包含非法字符。
        * **P2.2: 保存失败:** 网络/服务器错误。
        * **P2.3: 用户取消:** 用户在编辑页面点击返回或取消。
    * **P3: 修改昵称失败**
        * **P3.1: 前端校验失败:** 昵称过长或包含非法字符。
        * **P3.2: 保存失败:** 网络/服务器错误。
        * **P3.3: 用户取消:** 用户在编辑页面点击返回或取消。
    * **P4: 修改手机号失败**
        * **P4.1: 旧手机号验证失败:** 获取验证码失败（网络、服务、频率）；验证码错误/过期。
        * **P4.2: 新手机号验证失败:** 获取验证码失败（网络、服务、频率）；验证码错误/过期。
        * **P4.3: 新手机号已被占用:** 后台校验发现新手机号已被其他账号注册/绑定。
        * **P4.4: 最终确认修改失败:** 网络/服务器错误。
    * **P5: 退出登录失败:**
        * 网络错误导致退出指令发送失败。
    * **加载个人信息失败:**
        * 进入“我的”页面时，加载头像、姓名、手机号、昵称等信息失败（网络/服务器错误）。
    
## 3. 功能需求

### 3.1 绑定管理员 (对应 **UC-001**)

*   **3.1.1 功能描述:** 物业管理员首次使用App时，通过 **攸家运管** 平台提供的动态口令、口令密码以及个人手机号进行验证，完成账号与手机号的绑定，该手机号将作为其登录账号。
*   **3.1.2 需求详述:**
    *   **界面元素:** 登录页需提供“绑定管理员”入口；绑定管理员页面包含：动态口令输入框、口令密码输入框、姓名输入框、手机号码输入框、短信验证码输入框、获取验证码按钮、确认绑定按钮；绑定成功提示页面。

    ![图片](./img/登录/登录-口令.png)![图片](./img/登录/登录-设置管理员Step1.png)![图片](./img/登录/登录-设置管理员Step2.png)
    *   **交互逻辑:** (见 **UC-001**) 用户在登录页点击“绑定管理员” -> 进入绑定页面 -> 依次输入动态口令、口令密码、姓名、手机号 -> 点击获取验证码 -> 输入收到的验证码 -> 点击“确认绑定” -> 验证通过后显示成功提示页 -> 点击“我知道了并登录”返回登录页。
    *   **业务规则:** 动态口令及口令密码由 **【攸家运管】** 平台生成和提供，具有时效性；短信验证码规则（如长度、有效时间5分钟、发送频率限制）需与平台一致；绑定的手机号在物管员工库中必须唯一，不能是已注册的普通员工手机号；若小区已存在管理员，新的成功绑定将覆盖原有管理员信息。
    *   **权限控制:** 仅在小区未绑定管理员账号时，此功能新绑定会覆盖旧绑定。
    *   **异常处理:** (见 **UC-001**) 包括但不限于：口令/密码错误或过期、姓名/手机号为空或格式错误、手机号已被注册、验证码错误/过期/获取失败/频率超限、网络连接失败、服务器错误等情况，

### 3.2 登录与更新 (对应 **UC-002**, **UC-003**)

*   **3.2.1 功能描述:** 提供已注册员工（包括已绑定管理员）使用手机号和短信验证码登录App的功能，并在App启动时自动检查版本更新。
*   **3.2.2 需求详述:**
    *   **界面元素:** 登录页包含：手机号输入框、短信验证码输入框、获取验证码按钮、登录按钮；App启动时可能弹出版本更新提示框（包含版本号、更新内容、稍后升级/立即更新按钮）。
    ![图片](./img/登录/登录-验证码.png)![图片](./img/登录/登录-指纹验证.png)![图片](./img/APP升级/首页-APP升级.png)![图片](./img/APP升级/首页-APP升级中.png)
    *   **交互逻辑:** (见 **UC-002**, **UC-003**) App启动 -> 系统自动检查更新（若有新版，根据是否强制弹出相应提示框） -> 用户在登录页输入手机号 -> 点击获取验证码 -> 输入收到的验证码 -> 点击“登录” -> 验证成功后进入App主界面。
    *   **业务规则:** 登录账号必须是 **【攸家物管】** 员工管理中已添加且状态正常的员工手机号或通过动态口令绑定的账号；验证码规则同上；登录成功后服务端返回用户身份令牌（Token），建议有效期为30天；版本更新支持强制更新（用户必须更新才能继续使用）和建议更新（用户可选择稍后更新）。
    *   **权限控制:** 登录成功后，系统根据该员工的角色和权限（信息来源于 **【攸家运管】** 或 **【攸家物管】**），控制其在App内可见的功能模块和可执行的操作。
    *   **异常处理:** (见 **UC-002**, **UC-003**) 包括但不限于：手机号格式错误/未注册/无权限/账号被禁用、验证码错误/过期/获取失败、网络错误、服务器错误；更新检查失败、下载失败、安装失败等情况。


### 3.7 住户管理 (对应 **UC-004**, **UC-005**, **UC-006**, **UC-007**)
*   **3.7.1 功能描述:** 管理员查看和管理小区住户信息，包括按认证状态（已认证、待认证、未通过）查看列表、搜索/筛选住户、审批认证申请、修改住户信息、管理住户门禁凭证（录入门卡/人像、删除门卡）、下发凭证数据到门禁设备以及解绑住户与房产的关系。同时支持手动新增住户。
*   **3.7.2 需求详述:**
    *   **界面元素:** 住户管理Tab页；状态子Tab（已认证/待认证/未通过）；搜索框；筛选按钮；住户列表（显示头像、姓名、认证/申请时间、房产、电话、标签、凭证状态、删除门卡按钮、选择框）；底部操作栏（根据选中状态显示：未通过/通过、解绑/修改/录入门卡/录入人像/下发数据）；新增住户按钮("+")；新增/修改住户页面（手机号、验证码、姓名、户室选择、关系选择、确定登记/修改按钮）；凭证录入/下发相关界面。

    ![图片](./img/住户管理/住户管理-已认证.png)![图片](./img/住户管理/住户管理-待认证.png)![图片](./img/住户管理/住户管理-未通过.png)![图片](./img/住户管理/住户管理-筛选.png)![图片](./img/住户管理/住户管理-解绑.png)![图片](./img/住户管理/住户管理-确定未通过.png)![图片](./img/住户管理/住户管理-下发数据.png)![图片](./img/住户管理/登记住户/登记住户.png)![图片](./img/住户管理/登记住户/登记住户-选择住址.png)![图片](./img/住户管理/登记住户/登记住户-确认登记.png)![图片](./img/住户管理/登记住户/登记住户-提示录入人像_门.png)
    *   **交互逻辑:** (见 **UC-004**, **UC-005**) 查看列表（切换Tab、搜索、筛选、滚动加载）-> 选中住户（单选）-> 根据状态和选择执行底部操作（审批、解绑、修改、触发凭证录入[UC-006/007]、下发数据）-> 点击"+"进入新增流程（输入信息、选择户室/关系、验证手机、确认登记、可选后续凭证录入）-> 点击门卡删除按钮删除门卡。
    *   **业务规则:** 不同状态列表显示不同信息和操作；审批需记录原因（未通过）；修改时部分信息不可编辑（如手机号）；新增住户需验证手机号，记录登记方式为“物业登记”；门禁凭证管理调用相应UC；下发数据需选择目标设备；解绑解除住户与户室关联。
    *   **权限控制:** 需拥有住户管理权限。
    *   **异常处理:** (见 **UC-004**, **UC-005**) 列表加载/为空/搜索无结果；认证/解绑/修改/删除门卡/下发数据操作失败；新增时校验失败（空值、格式、验证码错误/过期、户室选择失败）；凭证录入失败（参考UC-006/007）；网络/服务器错误等。
### 3.8 组织发展 (员工与岗位管理) (对应 **UC-009**, **UC-006**, **UC-007**, **UC-008**)
*   **3.8.1 功能描述:** 管理员管理物业内部的组织架构，包括添加、查看、修改、删除员工信息，管理员工的岗位、门禁权限（设备通行权）、楼栋管理权限；以及创建、查看、修改、删除岗位，并定义岗位的功能权限（App内模块访问权）。支持为员工设置/录入/删除门禁凭证（密码/门卡/人像）并下发数据。
*   **3.8.2 需求详述:**
    *   **界面元素:** 组织发展Tab页；子Tab（员工管理、岗位管理）；员工列表（头像、姓名、岗位、手机号、注册时间、凭证状态、权限信息、选择框）；岗位列表（名称、权限摘要、选择框）；搜索框；新增按钮("+")；员工/岗位增改页面（手机号[员工新增]、验证码[员工新增]、姓名、岗位选择、门禁权限选择、楼栋权限选择[员工]、功能权限选择[岗位]、确定按钮）；底部操作栏（根据选择显示：修改/删除/设置密码/录入门卡/录入人像/下发数据）。

    ![图片](./img/组织发展/员工管理/员工管理.png)![图片](./img/组织发展/员工管理/登记员工.png)![图片](./img/组织发展/员工管理/登记员工-选择岗位.png)![图片](./img/组织发展/员工管理/登记员工-门禁_楼栋权限.png)![图片](./img/组织发展/员工管理/登记员工-确认登记.png)![图片](./img/组织发展/员工管理/登记员工-提示录入人像_门.png)![图片](./img/组织发展/岗位管理/岗位管理.png)![图片](./img/组织发展/岗位管理/添加岗位.png)![图片](./img/组织发展/岗位管理/添加岗位-选择功能.png)
    *   **交互逻辑:** (见 **UC-009**) 查看员工/岗位列表（切换子Tab、搜索、滚动）-> 选中员工/岗位 -> 点击"+"新增员工/岗位（输入信息、选择关联权限、验证[员工]、确认）-> 点击底部按钮执行修改/删除/凭证管理[UC-006/007/008]/下发数据（员工）。
    *   **业务规则:** 新增员工需验证手机号；员工需关联岗位、门禁权限、楼栋权限；岗位需关联功能权限；系统内置岗位及其权限；删除岗位会解绑员工关联；门禁凭证管理调用相应UC；下发数据需选择目标设备。
    *   **权限控制:** 需拥有组织发展管理权限。
    *   **异常处理:** (见 **UC-009**) 列表加载失败；新增/修改时校验失败（空值、格式、手机号已存在[员工]、验证码错误/过期[员工]、岗位名重复[岗位]）；保存/删除失败（网络/服务器错误）；凭证管理失败（参考UC-006/007/008）；获取验证码失败/频繁；未选择岗位/权限等。
### 3.9 个人门禁凭证管理 (对应 **UC-010**, **UC-006**, **UC-007**, **UC-008**)
*   **3.9.1 功能描述:** 物业员工在App内查看和管理自己的门禁凭证信息，包括查看密码、人像、门卡（可多张）的设置/录入状态，查看自己的门禁权限范围，并能发起设置密码、录入/删除门卡、录入人像以及将凭证数据下发到自己有权限的门禁设备的操作。
*   **3.9.2 需求详述:**
    *   **界面元素:** 门禁管理Tab页；页面顶部显示当前用户信息；凭证状态区域（密码状态、人像状态及照片、门卡列表及删除按钮）；门禁权限列表（只读）；底部操作栏（设置密码、录入门卡、录入人像、下发数据按钮）。

    ![图片](./img/门禁管理/门禁管理.png)![图片](./img/门禁管理/设置密码.png)![图片](./img/门禁管理/录入门卡.png)![图片](./img/门禁管理/录入人像-step1.png)![图片](./img/门禁管理/录入人像-step2.png)![图片](./img/门禁管理/录入人像-提示.png)
    *   **交互逻辑:** (见 **UC-010**) 进入门禁管理Tab -> 查看个人信息、凭证状态、权限范围 -> 点击“设置密码”进入密码设置流程（UC-008）-> 点击“录入门卡”进入NFC录卡流程（UC-007）-> 点击门卡旁的“删除”按钮删除该门卡（UC-007）-> 点击“录入人像”进入人脸采集流程（UC-006）-> 点击“下发数据”选择目标设备并确认，同步个人凭证。
    *   **业务规则:** 显示用户自身的凭证和权限信息；操作按钮触发对应的凭证管理UC；下发数据将个人凭证同步到有权限的设备。
    *   **权限控制:** 需拥有门禁管理权限才能访问此页面。
    *   **异常处理:** (见 **UC-010**, **UC-006**, **UC-007**, **UC-008**) 进入凭证管理流程失败（如NFC未开、无摄像头权限）；凭证设置/录入/删除失败；下发数据失败。
### 3.10 登记统计 (对应 **UC-011**)
*   **3.10.1 功能描述:** 管理员按楼栋/单元/户室查看住户的门禁凭证（人像、门卡、密码）登记状态，了解凭证录入覆盖情况。
*   **3.10.2 需求详述:**
    *   **界面元素:** 登记统计Tab页；筛选控件（楼栋/单元/户室）；清空按钮；户室分组列表；每个户室内显示住户卡片（姓名、凭证状态图标 - 人像/门卡/密码）。

    ![图片](./img/登记统计/登记统计-2.png)
    *   **交互逻辑:** (见 **UC-011**) 进入登记统计Tab -> 查看默认列表（按户室分组）-> 使用筛选控件选择范围 -> 查看筛选结果 -> 点击清空恢复默认 -> 滚动查看更多。
    *   **业务规则:** 数据按户室组织，展示每个住户的三种凭证（人像、门卡、密码）是否已登记的状态；支持按不同层级筛选。
    *   **权限控制:** 需拥有登记统计权限。
    *   **异常处理:** (见 **UC-011**) 列表加载失败；筛选后无结果；无任何住户/户室信息。
### 3.11 操作日志 (门禁管理) (对应 **UC-012**)
*   **3.11.1 功能描述:** 管理员查看门禁管理相关的系统操作日志，包括操作时间、操作人、操作模块、结果及对象，用于审计和问题排查。
*   **3.11.2 需求详述:**
    *   **界面元素:** 门禁日志Tab页；筛选控件（日期范围）；清空按钮；日志列表（显示操作时间、操作人、操作模块、操作结果、被操作对象[可选]）。

    ![图片](./img/门禁日志/门禁日志-2.png)![图片](./img/门禁日志/选择日期.png)
    *   **交互逻辑:** (见 **UC-012**) 进入门禁日志Tab -> 查看默认日志列表（时间倒序）-> 使用日期筛选控件选择日期范围 -> 查看筛选结果 -> 点击清空恢复默认 -> 滚动加载更多。
    *   **业务规则:** 记录管理员在门禁相关模块的操作行为（如登记/修改住户、下发数据、录入/删除凭证等）；支持按日期范围筛选。
    *   **权限控制:** 需拥有门禁日志权限。
    *   **异常处理:** (见 **UC-012**) 日志加载失败；筛选后无结果；无任何日志记录。
### 3.12 通知公告 (对应 **UC-013**)
*   **3.12.1 功能描述:** 管理员发布、查看、筛选、修改、撤回、删除、重新发布通知公告，管理公告的分类、接收范围、留言/私信权限，并查看住户留言。
*   **3.12.2 需求详述:**
    *   **界面元素:** 通知公告Tab页；筛选控件（时间范围）；清空按钮；通知列表（状态、分类标签、标题/摘要、发布者、时间、评论数）；新增按钮("+")；发布/编辑页面（分类选择、内容输入、图片/视频上传、标题设置、发布者名称设置、接收住户选择、允许留言/私信开关、发布/保存按钮）；通知详情页（完整内容、图片/视频、评论列表）；详情页底部操作栏（撤回/删除/修改/重新发布/允许留言/禁止留言/允许私信/禁止私信）。

    ![图片](./img/通知公告/通知公告.png) ![图片](./img/通知公告/发布管理-通知详情.png) ![图片](./img/通知公告/发布-通知公告.png) ![图片](./img/通知公告/发布-通知标题.png) ![图片](./img/通知公告/发布-发布者名称.png)
    *   **交互逻辑:** (见 **UC-013**) 查看列表（筛选、滚动）-> 点击"+"发布新通知（选分类、输内容、传附件、设标题/发布者、选范围、设权限、发布）-> 点击列表项查看详情（看内容、看评论、可选回复）-> 在列表或详情页执行管理操作（撤回、删除、修改、重发、切换留言/私信权限）。
    *   **业务规则:** 支持多种分类；可指定接收范围（楼栋/单元等）；可控制留言和私信；支持图片/视频附件；管理操作有相应状态变更和确认流程。
    *   **权限控制:** 需拥有通知公告管理权限。
    *   **异常处理:** (见 **UC-013**) 列表加载失败；发布/修改时校验失败或上传失败；管理操作失败；筛选无结果；网络/服务器错误。

### 3.13 电话管理 (对应 **UC-014**)
*   **3.13.1 功能描述:** 管理员添加、查看、修改、删除小区常用服务电话，并能直接从App内发起拨号。
*   **3.13.2 需求详述:**
    *   **界面元素:** 电话管理Tab页；电话列表（部门名称、号码、备注、选择框）；新增按钮("+")；添加/修改页面（部门名称、号码、备注输入框、确定按钮）；底部操作栏（修改/删除/直接拨号）。

     ![图片](./img/电话管理/电话管理.png) ![图片](./img/电话管理/添加电话.png)
    *   **交互逻辑:** (见 **UC-014**) 查看列表 -> 点击"+"添加新电话（输入信息、确定）-> 选中列表项 -> 点击底部按钮执行修改/删除/拨号操作。
    *   **业务规则:** 管理常用联系方式；支持直接调用系统拨号；添加的电话住户端可见。
    *   **权限控制:** 需拥有电话管理权限。
    *   **异常处理:** (见 **UC-014**) 列表加载失败；添加/修改时校验失败；删除/拨号时未选择；操作失败（网络/服务器错误）；设备无拨号功能。

### 3.14 设置 (对应 **UC-015**)
*   **3.14.1 功能描述:** 提供App级别的通用设置选项，包括查看关于信息、手动检查更新、注销账户以及退出当前登录。
*   **3.14.2 需求详述:**
    *   **界面元素:** 设置页面入口（通常在“我的”页面或主界面顶部）；设置项列表（关于我们、检查更新、注销账户）；退出登录按钮。

     ![图片](./img/设置/设置.png) ![图片](./img/设置/设置-关于我们.png) ![图片](./img/设置/设置-注销账户.png)
    *   **交互逻辑:** (见 **UC-015**) 进入设置页面 -> 点击“关于我们”查看App信息 -> 点击“检查更新”手动触发更新检查（UC-003流程）-> 点击“注销账户”进入注销流程 -> 点击“退出登录”确认后返回登录页（UC-002）。
    *   **业务规则:** 提供标准设置功能；退出登录需确认。
    *   **权限控制:** 所有登录用户均可访问。
    *   **异常处理:** (见 **UC-015**) 访问协议/政策失败；检查更新失败；进入注销流程失败。
### 3.15 个人信息管理 (对应 **UC-016**)
*   **3.15.1 功能描述:** 允许当前登录的用户查看和管理自己的个人资料，包括头像、姓名、昵称、以及绑定的手机号码（需验证）。
*   **3.15.2 需求详述:**
    *   **界面元素:** “我的”页面入口（通常是主界面顶部头像）；个人信息展示（头像、姓名、手机号、昵称）；修改入口（点击对应行或按钮）；头像更换选项（拍照/相册）；图片裁剪；姓名/昵称修改界面；修改手机号流程界面（验证旧、输入新、验证新）。

     ![图片](./img/我的/我的.png) ![图片](./img/我的/我的-修改头像.png) ![图片](./img/我的/我的-修改昵称.png) ![图片](./img/我的/我的-修改手机号.png)
    *   **交互逻辑:** (见 **UC-016**) 进入“我的”页面 -> 点击头像更换（选择来源、裁剪、上传）-> 点击姓名/昵称修改（输入、保存）-> 点击手机号修改（验证旧号、输入新号、验证新号、确认）。
    *   **业务规则:** 更换头像需权限，可裁剪；修改姓名/昵称直接保存；修改手机号需验证新旧手机，新手机号不能已被占用。
    *   **权限控制:** 用户只能修改自己的信息。
    *   **异常处理:** (见 **UC-016**) 加载信息失败；权限获取失败；图片操作/上传失败；保存姓名/昵称失败；手机号验证失败（验证码错误/过期/获取失败、手机号被占用）；网络/服务器错误。


## 4. 非功能需求 (Non-Functional Requirements)

*   **4.1 性能需求:**
    *   **响应时间:** 核心操作（如登录、远程开门、设备状态刷新）响应时间 < 1.5 秒；列表数据加载时间 < 3 秒（首屏）。
    *   **并发用户数:** 支持至少 100 个管理用户同时在线操作（需压力测试验证）。
    *   **资源消耗:** App 安装包大小 < 50MB (iOS/Android 分别考虑)；正常使用时内存占用和 CPU 消耗在合理范围，无明显卡顿或发热。
*   **4.2 可用性需求:**
    *   **易学性:** 新用户在无指导情况下，5 分钟内能完成登录、查看设备列表、执行一次远程开门操作。
    *   **易用性:** 常用操作路径清晰，步骤不超过 3 步。界面元素符合用户预期。
    *   **用户满意度:** (目标) V1.0 发布后 1 个月内，通过用户调研，核心功能满意度达到 80% 以上。
*   **4.3 可靠性需求:**
    *   **稳定性:** App 核心功能 Crash 率 < 0.1%。
    *   **数据一致性:** App 显示的状态/数据与后台、设备状态保持最终一致（允许网络延迟）。
    *   **错误恢复:** 网络中断后恢复时，App 能自动重连或提示用户手动刷新。操作失败有明确提示。
*   **4.4 安全性需求:**
    *   **传输安全:** App 与后台 API 通信必须使用 HTTPS 加密。
    *   **数据安全:** 用户密码（若有）、敏感配置信息（如设备密钥）需加密存储。验证码短信内容避免包含敏感信息。导出数据需进行权限校验。
    *   **认证安全:** 防止暴力破解登录（如验证码尝试次数限制）。登录状态 Session/Token 有效期及刷新机制。
    *   **访问控制:** 严格执行基于角色的权限控制（即使 V1.0 简化，后台需支持）。
    *   **防逆向:** 进行基本的代码混淆和加固。
*   **4.5 可维护性需求:**
    *   **代码规范:** 遵循团队统一的 iOS/Android 编码规范。关键代码有清晰注释。
    *   **模块化:** 功能模块清晰分离，降低耦合度。
    *   **配置化:** 服务器地址、关键参数（如验证码有效期、重试次数）应可配置。
*   **4.6 可扩展性需求:**
    *   **架构设计:** 考虑未来接入更多类型的智能设备。API 设计遵循 RESTful 风格，易于扩展。
    *   **人员/设备类型:** 注册流程和参数配置应易于扩展支持新的类型和属性。
*   **4.7 兼容性需求:**
    *   **操作系统:** 支持 iOS 13.0 及以上版本，Android 6.0 及以上版本。
    *   **设备:** 兼容主流手机品牌和屏幕尺寸（如 iPhone 8 及以上，主流 Android 厂商近 3 年发布的手机）。完成在主要分辨率上的测试。



## 6. 界面与交互设计 (UI/UX Design)

*   **6.1 设计原则:**
    *   **一致性:** 遵循统一的设计风格（参考 Mockup）、色彩（主色调用橙色）、字体、图标和布局。
    *   **清晰性:** 信息层级分明，重要信息突出。操作按钮语义明确。
    *   **效率性:** 针对管理后台特性，优化高频操作路径，减少点击次数。
    *   **反馈及时:** 用户操作后（尤其耗时或后台操作），给予明确的加载、成功或失败反馈。
    *   **容错性:** 提供撤销或确认步骤（如删除、解绑操作），减少误操作风险。输入校验友好。
*   **6.2 整体交互流程:**
    *   App 启动 -> 检查更新 -> (若未登录) 登录页 -> (若已登录) 主界面。
    *   主界面采用底部 Tab 导航（推测，原型未完全展示），包含“设备”、“人员”（住户/员工可能合并或二级入口）、“社区”（互助/举报可能合并或二级入口）、“我的”等主要模块。
    *   各模块内采用列表 -> 详情 -> 操作/编辑 的标准流程。
    *   复杂操作（如注册、筛选、参数设置）使用弹窗或新页面承载。
*   **6.3 (参考) 界面原型/视觉设计:**
    *   **核心依据:** 本 PRD 强依赖于提供的 UI Mockups 文件 `[攸家 Mockup 文件名或链接]`。所有界面元素的布局、样式、内容均以此为准。
    *   **交互细节:** 如动画效果、转场方式等未在 Mockup 中明确的，由 UI/UX 设计师补充说明，或采用平台默认效果。

---

## 7. 发布标准 (Release Criteria)

*   **功能性:**
    *   所有 V2.1.0 Scope 内的 Use Cases 均已实现并通过测试。
    *   主要功能流程无阻塞性 Bug。
*   **稳定性:**
    *   所有 P0、P1 级别的 Bug 必须修复。
    *   连续运行 48 小时无 Crash 或严重性能问题。
    *   Crash 率低于 0.1%。
*   **性能:**
    *   核心操作响应时间、资源消耗等性能指标达到第 4.1 节要求。
    *   通过至少 50 用户并发的压力测试。
*   **兼容性:**
    *   在目标 iOS 和 Android 版本及主流设备上测试通过，无严重兼容性问题。
*   **安全性:**
    *   通过基本安全测试，无明显漏洞（如明文传输、越权访问）。
*   **文档:**
    *   测试报告完成。
    *   用户手册/操作指南初稿完成（若需要）。
*   **验收:**
    *   产品、测试、开发团队共同确认达到发布标准。

---

## 8. 未来考虑 / 待办事项 (Future Considerations / Open Issues)

*   **未来功能迭代考虑:**
    *   业主/住户端 App 开发与联动。
    *   在线缴费功能集成。
    *   报事报修工单系统。
    *   访客预约与管理系统。
    *   智能硬件（如道闸、监控）联动控制。
    *   更详细的数据分析报表和可视化 Dashboard。
    *   消息推送系统 (业务通知、提醒)。
    *   员工考勤/排班功能。
