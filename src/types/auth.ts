/**
 * 认证相关类型定义
 * 对应UC-001: 首次绑定管理员账号
 * 对应UC-002: 物业员工登录App
 */

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',           // 管理员
  HOUSEKEEPER = 'housekeeper', // 管家
  SECURITY = 'security',     // 保安
  CLEANER = 'cleaner',       // 保洁
  MAINTENANCE = 'maintenance', // 维修
  GARDENER = 'gardener',     // 绿化
}

// 权限枚举
export enum Permission {
  // 住户管理权限
  RESIDENTS_VIEW = 'residents:view',
  RESIDENTS_MANAGE = 'residents:manage',
  RESIDENTS_APPROVE = 'residents:approve',

  // 员工管理权限
  STAFF_VIEW = 'staff:view',
  STAFF_MANAGE = 'staff:manage',

  // 岗位管理权限
  POSITIONS_VIEW = 'positions:view',
  POSITIONS_MANAGE = 'positions:manage',

  // 门禁管理权限
  ACCESS_VIEW = 'access:view',
  ACCESS_MANAGE = 'access:manage',
  ACCESS_CONTROL = 'access:control',

  // 统计查看权限
  STATISTICS_VIEW = 'statistics:view',

  // 日志查看权限
  LOGS_VIEW = 'logs:view',

  // 通知公告权限
  NOTICES_VIEW = 'notices:view',
  NOTICES_MANAGE = 'notices:manage',

  // 电话管理权限
  PHONES_VIEW = 'phones:view',
  PHONES_MANAGE = 'phones:manage',

  // 系统设置权限
  SETTINGS_MANAGE = 'settings:manage',
}

// 用户基本信息接口
export interface User {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  nickname?: string;
  role: UserRole;
  positions: string[];
  permissions: Permission[];
  accessRights: string[];      // 门禁权限
  buildingRights: string[];    // 楼栋权限
  community: {
    id: string;
    name: string;
  };
  registrationTime: string;
  lastLoginTime?: string;
}

// 认证状态接口
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
}

// 登录凭据接口
export interface LoginCredentials {
  phone: string;
  verificationCode: string;
}

// 管理员绑定数据接口 (UC-001)
export interface BindAdminData {
  dynamicCode: string;        // 动态口令
  password: string;           // 口令密码
  name: string;               // 管理员姓名
  phone: string;              // 手机号
  verificationCode: string;   // 短信验证码
  captcha?: string;           // 图形验证码
}

// 验证码类型
export enum VerificationCodeType {
  LOGIN = 'login',           // 登录验证码
  BIND_ADMIN = 'bind_admin', // 绑定管理员验证码
  CHANGE_PHONE = 'change_phone', // 修改手机号验证码
}

// 认证错误类型
export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

// 认证响应接口
export interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    token: string;
    refreshToken: string;
  };
  error?: AuthError;
}
