/**
 * 认证相关Mock数据
 * 对应UC-001: 首次绑定管理员账号
 * 对应UC-002: 物业员工登录App
 */

import { BindAdminData, LoginCredentials, User, UserRole, Permission, AuthResponse } from '@/types/auth';

// Mock延迟函数
const mockDelay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// 动态口令和密码配置
export const MOCK_ADMIN_CODES = {
  dynamicCode: '123456',
  password: 'admin123',
};

// 验证码配置
export const MOCK_VERIFICATION_CODES = {
  '13800138000': '1234',
  '13800138001': '1234',
  '13800138002': '1234',
  default: '1234',
};

// 图形验证码配置
export const MOCK_CAPTCHA_CODES = {
  'ABCD': true,
  'EFGH': true,
  'IJKL': true,
  default: 'ABCD',
};

// Mock管理员用户数据
export const MOCK_ADMIN_USER: User = {
  id: 'admin_001',
  name: '张管理员',
  phone: '13800138000',
  avatar: '',
  nickname: '张总',
  role: UserRole.ADMIN,
  positions: ['housekeeper'],
  permissions: [
    Permission.RESIDENTS_VIEW,
    Permission.RESIDENTS_MANAGE,
    Permission.RESIDENTS_APPROVE,
    Permission.STAFF_VIEW,
    Permission.STAFF_MANAGE,
    Permission.POSITIONS_VIEW,
    Permission.POSITIONS_MANAGE,
    Permission.ACCESS_VIEW,
    Permission.ACCESS_MANAGE,
    Permission.ACCESS_CONTROL,
    Permission.STATISTICS_VIEW,
    Permission.LOGS_VIEW,
    Permission.NOTICES_VIEW,
    Permission.NOTICES_MANAGE,
    Permission.PHONES_VIEW,
    Permission.PHONES_MANAGE,
    Permission.SETTINGS_MANAGE,
  ],
  accessRights: ['building_1', 'building_2', 'building_3'],
  buildingRights: ['building_1', 'building_2', 'building_3'],
  community: {
    id: 'community_001',
    name: '阳光花园小区',
  },
  registrationTime: new Date().toISOString(),
  lastLoginTime: new Date().toISOString(),
};

// Mock员工用户数据
export const MOCK_STAFF_USERS: User[] = [
  {
    id: 'staff_001',
    name: '李保安',
    phone: '13800138001',
    avatar: '',
    role: UserRole.SECURITY,
    positions: ['security'],
    permissions: [
      Permission.RESIDENTS_VIEW,
      Permission.ACCESS_VIEW,
      Permission.ACCESS_CONTROL,
      Permission.LOGS_VIEW,
    ],
    accessRights: ['building_1', 'building_2'],
    buildingRights: ['building_1', 'building_2'],
    community: {
      id: 'community_001',
      name: '阳光花园小区',
    },
    registrationTime: new Date().toISOString(),
    lastLoginTime: new Date().toISOString(),
  },
  {
    id: 'staff_002',
    name: '王管家',
    phone: '13800138002',
    avatar: '',
    role: UserRole.HOUSEKEEPER,
    positions: ['housekeeper'],
    permissions: [
      Permission.RESIDENTS_VIEW,
      Permission.RESIDENTS_MANAGE,
      Permission.ACCESS_VIEW,
      Permission.ACCESS_MANAGE,
      Permission.NOTICES_VIEW,
      Permission.NOTICES_MANAGE,
    ],
    accessRights: ['building_1', 'building_2', 'building_3'],
    buildingRights: ['building_1', 'building_2', 'building_3'],
    community: {
      id: 'community_001',
      name: '阳光花园小区',
    },
    registrationTime: new Date().toISOString(),
    lastLoginTime: new Date().toISOString(),
  },
];

// 管理员绑定Mock服务
export const mockBindAdmin = async (data: BindAdminData): Promise<AuthResponse> => {
  await mockDelay(1500);

  // 验证动态口令
  if (data.dynamicCode !== MOCK_ADMIN_CODES.dynamicCode) {
    return {
      success: false,
      error: {
        code: 'INVALID_DYNAMIC_CODE',
        message: '动态口令错误',
      },
    };
  }

  // 验证口令密码
  if (data.password !== MOCK_ADMIN_CODES.password) {
    return {
      success: false,
      error: {
        code: 'INVALID_PASSWORD',
        message: '口令密码错误',
      },
    };
  }

  // 验证短信验证码
  const expectedCode = MOCK_VERIFICATION_CODES[data.phone] || MOCK_VERIFICATION_CODES.default;
  if (data.verificationCode !== expectedCode) {
    return {
      success: false,
      error: {
        code: 'INVALID_VERIFICATION_CODE',
        message: '验证码错误',
      },
    };
  }

  // 验证图形验证码（如果提供）
  if (data.captcha && !MOCK_CAPTCHA_CODES[data.captcha]) {
    return {
      success: false,
      error: {
        code: 'INVALID_CAPTCHA',
        message: '图形验证码错误',
      },
    };
  }

  // 绑定成功，返回管理员用户信息
  const adminUser: User = {
    ...MOCK_ADMIN_USER,
    name: data.name,
    phone: data.phone,
    id: 'admin_' + Date.now(),
    registrationTime: new Date().toISOString(),
  };

  return {
    success: true,
    data: {
      user: adminUser,
      token: 'mock_admin_token_' + Date.now(),
      refreshToken: 'mock_admin_refresh_token_' + Date.now(),
    },
  };
};

// 员工登录Mock服务
export const mockLogin = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  await mockDelay(1000);

  // 验证短信验证码
  const expectedCode = MOCK_VERIFICATION_CODES[credentials.phone] || MOCK_VERIFICATION_CODES.default;
  if (credentials.verificationCode !== expectedCode) {
    return {
      success: false,
      error: {
        code: 'INVALID_VERIFICATION_CODE',
        message: '验证码错误',
      },
    };
  }

  // 查找用户
  let user: User | undefined;
  
  // 检查是否是管理员
  if (credentials.phone === MOCK_ADMIN_USER.phone) {
    user = MOCK_ADMIN_USER;
  } else {
    // 查找员工用户
    user = MOCK_STAFF_USERS.find(u => u.phone === credentials.phone);
  }

  if (!user) {
    return {
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: '用户不存在，请联系管理员',
      },
    };
  }

  // 登录成功
  return {
    success: true,
    data: {
      user: {
        ...user,
        lastLoginTime: new Date().toISOString(),
      },
      token: 'mock_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
    },
  };
};

// 发送验证码Mock服务
export const mockSendVerificationCode = async (phone: string): Promise<{ success: boolean; message: string }> => {
  await mockDelay(800);

  // 模拟发送成功
  return {
    success: true,
    message: '验证码已发送',
  };
};

// 获取图形验证码Mock服务
export const mockGetCaptcha = async (): Promise<{ code: string; image: string }> => {
  await mockDelay(300);

  const codes = Object.keys(MOCK_CAPTCHA_CODES);
  const randomCode = codes[Math.floor(Math.random() * codes.length)];

  return {
    code: randomCode,
    image: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`, // 1x1透明图片
  };
};
