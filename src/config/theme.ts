/**
 * 攸家物管App主题配置
 * 橙色主色调 #FF6B35，8px基础间距
 */
export const theme = {
  colors: {
    // 主色调 - 橙色系
    primary: '#FF6B35',
    primaryLight: '#FFE5DC',
    primaryDark: '#E55A2B',

    // 辅助色
    secondary: '#2196F3',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    danger: '#F44336',
    info: '#2196F3',

    // 背景色
    background: '#FFFFFF',
    backgroundSecondary: '#F8F9FA',
    surface: '#F5F5F5',
    surfaceSecondary: '#FAFAFA',

    // 基础色
    white: '#FFFFFF',
    black: '#000000',
    transparent: 'transparent',

    // 文字色
    text: '#212121',
    textSecondary: '#757575',
    textTertiary: '#9E9E9E',
    textDisabled: '#BDBDBD',
    textInverse: '#FFFFFF',

    // 边框和分割线
    border: '#E0E0E0',
    borderLight: '#F0F0F0',
    divider: '#EEEEEE',

    // 状态色
    statusSuccess: '#E8F5E8',
    statusWarning: '#FFF3E0',
    statusError: '#FFEBEE',
    statusInfo: '#E3F2FD',

    // 认证状态色
    verified: '#4CAF50',      // 已认证
    pending: '#FF9800',       // 待认证
    rejected: '#F44336',      // 未通过
  },

  typography: {
    // 标题
    h1: { fontSize: 28, fontWeight: '700', lineHeight: 36 },
    h2: { fontSize: 24, fontWeight: '700', lineHeight: 32 },
    h3: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
    h4: { fontSize: 18, fontWeight: '600', lineHeight: 24 },
    h5: { fontSize: 16, fontWeight: '600', lineHeight: 22 },

    // 正文
    body1: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
    body2: { fontSize: 14, fontWeight: '400', lineHeight: 20 },

    // 辅助文字
    caption: { fontSize: 12, fontWeight: '400', lineHeight: 16 },
    overline: { fontSize: 10, fontWeight: '500', lineHeight: 14 },

    // 按钮文字
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
    buttonSmall: { fontSize: 14, fontWeight: '600', lineHeight: 18 },

    // 标签文字
    label: { fontSize: 14, fontWeight: '500', lineHeight: 18 },
    labelSmall: { fontSize: 12, fontWeight: '500', lineHeight: 16 },
  },

  spacing: {
    // 基础间距 8px
    xs: 4,   // 0.5x
    sm: 8,   // 1x
    md: 16,  // 2x
    lg: 24,  // 3x
    xl: 32,  // 4x
    xxl: 40, // 5x

    // 特殊间距
    tiny: 2,
    huge: 48,

    // 页面间距
    pageHorizontal: 16,
    pageVertical: 24,

    // 组件间距
    componentGap: 12,
    sectionGap: 20,
  },

  borderRadius: {
    none: 0,
    xs: 2,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 20,
    full: 9999,
  },

  shadows: {
    none: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    sm: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 8,
    },
  },

  // 组件尺寸
  sizes: {
    // 按钮高度
    buttonSmall: 32,
    buttonMedium: 40,
    buttonLarge: 48,

    // 输入框高度
    inputSmall: 32,
    inputMedium: 40,
    inputLarge: 48,

    // 头像尺寸
    avatarSmall: 24,
    avatarMedium: 32,
    avatarLarge: 48,
    avatarXLarge: 64,

    // 图标尺寸
    iconSmall: 16,
    iconMedium: 20,
    iconLarge: 24,
    iconXLarge: 32,
  },

  // 动画配置
  animation: {
    duration: {
      fast: 150,
      normal: 250,
      slow: 350,
    },
    easing: {
      easeInOut: 'ease-in-out',
      easeOut: 'ease-out',
      easeIn: 'ease-in',
    },
  },
};

export type Theme = typeof theme;
