/**
 * 图形验证码组件
 * 对应UC-001管理员绑定页面的图形验证码需求
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { theme } from '@/config/theme';
import { mockGetCaptcha } from '@/data/authMockData';

export interface CaptchaProps {
  value?: string;
  onChangeText?: (text: string) => void;
  error?: string;
  disabled?: boolean;
  containerStyle?: ViewStyle;
  onRefresh?: () => void;
}

export const Captcha: React.FC<CaptchaProps> = ({
  value = '',
  onChangeText,
  error,
  disabled = false,
  containerStyle,
  onRefresh,
}) => {
  const [captchaData, setCaptchaData] = useState<{
    code: string;
    image: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);

  // 获取验证码
  const fetchCaptcha = async () => {
    setLoading(true);
    try {
      const data = await mockGetCaptcha();
      setCaptchaData(data);
      onRefresh?.();
    } catch (error) {
      console.error('获取验证码失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取验证码
  useEffect(() => {
    fetchCaptcha();
  }, []);

  // 刷新验证码
  const handleRefresh = () => {
    if (!loading && !disabled) {
      fetchCaptcha();
    }
  };

  const hasError = Boolean(error);

  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={[styles.label, hasError && styles.errorLabel]}>
        图形验证码
      </Text>
      
      <View style={styles.captchaContainer}>
        {/* 验证码图片 */}
        <TouchableOpacity
          style={[
            styles.imageContainer,
            hasError && styles.errorBorder,
            disabled && styles.disabled,
          ]}
          onPress={handleRefresh}
          disabled={disabled || loading}
          activeOpacity={0.7}
        >
          {captchaData ? (
            <View style={styles.imageWrapper}>
              {/* 模拟验证码图片 - 显示文字 */}
              <View style={styles.mockImage}>
                <Text style={styles.captchaText}>
                  {captchaData.code}
                </Text>
              </View>
              
              {/* 刷新提示 */}
              <View style={styles.refreshHint}>
                <Text style={styles.refreshText}>
                  点击刷新
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>
                {loading ? '加载中...' : '获取失败'}
              </Text>
            </View>
          )}
        </TouchableOpacity>
        
        {/* 刷新按钮 */}
        <TouchableOpacity
          style={[
            styles.refreshButton,
            disabled && styles.disabledButton,
          ]}
          onPress={handleRefresh}
          disabled={disabled || loading}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.refreshButtonText,
            disabled && styles.disabledButtonText,
          ]}>
            {loading ? '...' : '刷新'}
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* 错误提示 */}
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}
      
      {/* 使用提示 */}
      <Text style={styles.helperText}>
        请输入图片中的字符，不区分大小写
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },

  label: {
    fontSize: theme.typography.label.fontSize,
    fontWeight: theme.typography.label.fontWeight as any,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },

  errorLabel: {
    color: theme.colors.error,
  },

  captchaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },

  imageContainer: {
    width: 120,
    height: 40,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.white,
    overflow: 'hidden',
  },

  errorBorder: {
    borderColor: theme.colors.error,
  },

  disabled: {
    backgroundColor: theme.colors.surface,
    opacity: 0.6,
  },

  imageWrapper: {
    flex: 1,
    position: 'relative',
  },

  mockImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },

  captchaText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
    letterSpacing: 2,
    textTransform: 'uppercase',
  },

  refreshHint: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 1,
  },

  refreshText: {
    fontSize: 8,
    color: theme.colors.white,
    textAlign: 'center',
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },

  loadingText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
  },

  refreshButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    minWidth: 60,
    alignItems: 'center',
  },

  disabledButton: {
    backgroundColor: theme.colors.textDisabled,
  },

  refreshButtonText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.white,
    fontWeight: '600',
  },

  disabledButtonText: {
    color: theme.colors.white,
  },

  errorText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.xs,
  },

  helperText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.xs,
  },
});
