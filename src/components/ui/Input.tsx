/**
 * 输入框组件
 * 支持多种类型、状态和验证
 */

import React, { useState, forwardRef } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { theme } from '@/config/theme';

export type InputSize = 'small' | 'medium' | 'large';
export type InputType = 'text' | 'password' | 'phone' | 'number' | 'email';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  placeholder?: string;
  type?: InputType;
  size?: InputSize;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
}

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  placeholder,
  type = 'text',
  size = 'medium',
  error,
  helperText,
  required = false,
  disabled = false,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  labelStyle,
  value,
  onChangeText,
  ...props
}, ref) => {
  const [focused, setFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const hasError = Boolean(error);
  const displayHelperText = error || helperText;

  // 根据类型设置键盘类型
  const getKeyboardType = () => {
    switch (type) {
      case 'phone':
        return 'phone-pad';
      case 'number':
        return 'numeric';
      case 'email':
        return 'email-address';
      default:
        return 'default';
    }
  };

  // 处理密码显示切换
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const containerStyles = [
    styles.container,
    containerStyle,
  ];

  const inputContainerStyles = [
    styles.inputContainer,
    styles[size],
    focused && styles.focused,
    hasError && styles.error,
    disabled && styles.disabled,
  ];

  const inputStyles = [
    styles.input,
    styles[`${size}Input`],
    disabled && styles.disabledInput,
    inputStyle,
  ];

  const labelStyles = [
    styles.label,
    required && styles.requiredLabel,
    hasError && styles.errorLabel,
    labelStyle,
  ];

  return (
    <View style={containerStyles}>
      {label && (
        <Text style={labelStyles}>
          {label}
          {required && <Text style={styles.asterisk}> *</Text>}
        </Text>
      )}
      
      <View style={inputContainerStyles}>
        {leftIcon && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          ref={ref}
          style={inputStyles}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.textTertiary}
          value={value}
          onChangeText={onChangeText}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          editable={!disabled}
          secureTextEntry={type === 'password' && !showPassword}
          keyboardType={getKeyboardType()}
          autoCapitalize={type === 'email' ? 'none' : 'sentences'}
          autoCorrect={type !== 'email' && type !== 'password'}
          {...props}
        />
        
        {type === 'password' && (
          <TouchableOpacity
            style={styles.rightIcon}
            onPress={togglePasswordVisibility}
          >
            <Text style={styles.passwordToggle}>
              {showPassword ? '隐藏' : '显示'}
            </Text>
          </TouchableOpacity>
        )}
        
        {rightIcon && type !== 'password' && (
          <TouchableOpacity
            style={styles.rightIcon}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </View>
      
      {displayHelperText && (
        <Text style={[
          styles.helperText,
          hasError && styles.errorText,
        ]}>
          {displayHelperText}
        </Text>
      )}
    </View>
  );
});

Input.displayName = 'Input';

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },

  label: {
    fontSize: theme.typography.label.fontSize,
    fontWeight: theme.typography.label.fontWeight as any,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },

  requiredLabel: {
    // 必填标签样式
  },

  asterisk: {
    color: theme.colors.error,
  },

  errorLabel: {
    color: theme.colors.error,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
  },

  // 尺寸样式
  small: {
    height: theme.sizes.inputSmall,
  },
  medium: {
    height: theme.sizes.inputMedium,
  },
  large: {
    height: theme.sizes.inputLarge,
  },

  // 状态样式
  focused: {
    borderColor: theme.colors.primary,
    ...theme.shadows.sm,
  },

  error: {
    borderColor: theme.colors.error,
  },

  disabled: {
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.borderLight,
  },

  input: {
    flex: 1,
    fontSize: theme.typography.body1.fontSize,
    color: theme.colors.text,
    paddingHorizontal: theme.spacing.md,
  },

  smallInput: {
    fontSize: theme.typography.body2.fontSize,
  },

  mediumInput: {
    fontSize: theme.typography.body1.fontSize,
  },

  largeInput: {
    fontSize: theme.typography.body1.fontSize,
  },

  disabledInput: {
    color: theme.colors.textDisabled,
  },

  leftIcon: {
    paddingLeft: theme.spacing.md,
    paddingRight: theme.spacing.xs,
  },

  rightIcon: {
    paddingRight: theme.spacing.md,
    paddingLeft: theme.spacing.xs,
  },

  passwordToggle: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.primary,
    fontWeight: '500',
  },

  helperText: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    marginLeft: theme.spacing.xs,
  },

  errorText: {
    color: theme.colors.error,
  },
});
