/**
 * 按钮组件
 * 支持多种变体、尺寸和状态
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';
import { theme } from '@/config/theme';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  children,
  style,
  textStyle,
  onPress,
  ...props
}) => {
  const isDisabled = disabled || loading;

  const handlePress = (event: any) => {
    if (isDisabled) return;
    onPress?.(event);
  };

  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    isDisabled && styles.disabled,
    isDisabled && styles[`${variant}Disabled`],
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    isDisabled && styles.disabledText,
    isDisabled && styles[`${variant}DisabledText`],
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={handlePress}
      disabled={isDisabled}
      activeOpacity={0.7}
      {...props}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? theme.colors.primary : theme.colors.white}
          style={styles.loader}
        />
      )}
      {typeof children === 'string' ? (
        <Text style={textStyleCombined}>{children}</Text>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: 'transparent',
  },

  // 变体样式
  primary: {
    backgroundColor: theme.colors.primary,
  },
  secondary: {
    backgroundColor: theme.colors.secondary,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  danger: {
    backgroundColor: theme.colors.error,
  },

  // 尺寸样式
  small: {
    height: theme.sizes.buttonSmall,
    paddingHorizontal: theme.spacing.md,
  },
  medium: {
    height: theme.sizes.buttonMedium,
    paddingHorizontal: theme.spacing.lg,
  },
  large: {
    height: theme.sizes.buttonLarge,
    paddingHorizontal: theme.spacing.xl,
  },

  // 全宽样式
  fullWidth: {
    width: '100%',
  },

  // 禁用样式
  disabled: {
    opacity: 0.6,
  },
  primaryDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },
  secondaryDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },
  outlineDisabled: {
    borderColor: theme.colors.textDisabled,
  },
  ghostDisabled: {},
  dangerDisabled: {
    backgroundColor: theme.colors.textDisabled,
  },

  // 文字样式
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryText: {
    color: theme.colors.white,
  },
  secondaryText: {
    color: theme.colors.white,
  },
  outlineText: {
    color: theme.colors.primary,
  },
  ghostText: {
    color: theme.colors.primary,
  },
  dangerText: {
    color: theme.colors.white,
  },

  // 文字尺寸
  smallText: {
    fontSize: theme.typography.buttonSmall.fontSize,
    lineHeight: theme.typography.buttonSmall.lineHeight,
  },
  mediumText: {
    fontSize: theme.typography.button.fontSize,
    lineHeight: theme.typography.button.lineHeight,
  },
  largeText: {
    fontSize: theme.typography.button.fontSize,
    lineHeight: theme.typography.button.lineHeight,
  },

  // 禁用文字样式
  disabledText: {},
  primaryDisabledText: {
    color: theme.colors.white,
  },
  secondaryDisabledText: {
    color: theme.colors.white,
  },
  outlineDisabledText: {
    color: theme.colors.textDisabled,
  },
  ghostDisabledText: {
    color: theme.colors.textDisabled,
  },
  dangerDisabledText: {
    color: theme.colors.white,
  },

  // 加载器样式
  loader: {
    marginRight: theme.spacing.sm,
  },
});
