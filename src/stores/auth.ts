import { User } from '@/types/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AuthState {
  // 状态
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;

  // 权限相关
  permissions: string[];
  roles: string[];

  // 操作方法
  login: (token: string, user: User, refreshToken?: string) => void;
  bindAdmin: (user: User, token: string, refreshToken: string) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  setRefreshToken: (refreshToken: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setPermissions: (permissions: string[]) => void;
  setRoles: (roles: string[]) => void;
  clearError: () => void;

  // 权限检查方法
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;

  // 初始化和状态检查方法
  initializeAuth: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      isLoading: false,
      error: null,
      permissions: [],
      roles: [],

      // 登录方法
      login: (token: string, user: User, refreshToken?: string) => {
        set({
          isAuthenticated: true,
          user,
          token,
          refreshToken,
          error: null,
          isLoading: false,
          permissions: user.permissions.map(p => p.toString()),
          roles: [user.role.toString()],
        });
      },

      // 管理员绑定方法
      bindAdmin: async (user: User, token: string, refreshToken: string) => {
        try {
          // 保存敏感信息到SecureStore
          await SecureStore.setItemAsync('auth_token', token);
          await SecureStore.setItemAsync('refresh_token', refreshToken);

          set({
            isAuthenticated: true,
            user,
            token,
            refreshToken,
            error: null,
            isLoading: false,
            permissions: user.permissions.map(p => p.toString()),
            roles: [user.role.toString()],
          });
        } catch (error) {
          console.error('保存管理员认证信息失败:', error);
          set({
            error: '保存认证信息失败',
            isLoading: false,
          });
          throw error;
        }
      },

      // 登出方法
      logout: async () => {
        try {
          // 清除SecureStore中的敏感信息
          await SecureStore.deleteItemAsync('auth_token');
          await SecureStore.deleteItemAsync('refresh_token');
        } catch (error) {
          console.error('清除认证信息失败:', error);
        }

        set({
          isAuthenticated: false,
          user: null,
          token: null,
          refreshToken: null,
          permissions: [],
          roles: [],
          error: null,
          isLoading: false,
        });
      },

      // 设置用户信息
      setUser: (user: User) => {
        set({ user });
      },

      // 设置访问令牌
      setToken: (token: string) => {
        set({ token, isAuthenticated: !!token });
      },

      // 设置刷新令牌
      setRefreshToken: (refreshToken: string) => {
        set({ refreshToken });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      },

      // 设置权限
      setPermissions: (permissions: string[]) => {
        set({ permissions });
      },

      // 设置角色
      setRoles: (roles: string[]) => {
        set({ roles });
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 权限检查方法
      hasPermission: (permission: string) => {
        const { permissions } = get();
        return permissions.includes(permission);
      },

      hasRole: (role: string) => {
        const { roles } = get();
        return roles.includes(role);
      },

      hasAnyPermission: (permissions: string[]) => {
        const { permissions: userPermissions } = get();
        return permissions.some(permission =>
          userPermissions.includes(permission)
        );
      },

      hasAllPermissions: (permissions: string[]) => {
        const { permissions: userPermissions } = get();
        return permissions.every(permission =>
          userPermissions.includes(permission)
        );
      },

      // 初始化认证状态
      initializeAuth: async () => {
        set({ isLoading: true });
        try {
          // 这里可以添加从存储中恢复认证状态的逻辑
          // 当前由于使用了persist中间件，状态会自动恢复
          const { token } = get();
          if (token) {
            // 可以在这里验证token的有效性
            // 暂时直接设置为已认证
            set({ isAuthenticated: true, isLoading: false });
          } else {
            set({ isAuthenticated: false, isLoading: false });
          }
        } catch (error) {
          console.error('Initialize auth failed:', error);
          set({
            isAuthenticated: false,
            isLoading: false,
            error: '初始化认证状态失败',
          });
        }
      },

      // 检查认证状态
      checkAuthStatus: async () => {
        set({ isLoading: true });
        try {
          const { token } = get();
          if (token) {
            // 这里可以调用API验证token有效性
            // 暂时直接设置为已认证
            set({ isAuthenticated: true, isLoading: false });
          } else {
            set({ isAuthenticated: false, isLoading: false });
          }
        } catch (error) {
          console.error('Check auth status failed:', error);
          set({
            isAuthenticated: false,
            isLoading: false,
            error: '检查认证状态失败',
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        permissions: state.permissions,
        roles: state.roles,
      }),
    }
  )
);

// 初始化时从SecureStore恢复Token
export const initializeAuth = async () => {
  try {
    const token = await SecureStore.getItemAsync('auth_token');
    const refreshToken = await SecureStore.getItemAsync('refresh_token');

    if (token && refreshToken) {
      useAuthStore.setState({
        token,
        refreshToken,
      });
    }
  } catch (error) {
    console.error('初始化认证状态失败:', error);
  }
};
