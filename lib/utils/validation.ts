/**
 * 表单验证工具
 */

// 手机号正则表达式
const PHONE_REGEX = /^1[3-9]\d{9}$/;

// 身份证号正则表达式
const ID_CARD_REGEX = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

// 密码强度正则表达式（至少6位，包含字母和数字）
const PASSWORD_REGEX = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;

// 验证码正则表达式（6位数字）
const VERIFICATION_CODE_REGEX = /^\d{6}$/;

// 房间号正则表达式
const ROOM_REGEX = /^\d{3,4}$/;

/**
 * 验证手机号
 * @param phone 手机号
 * @returns 验证结果
 */
export const validatePhone = (phone: string): { isValid: boolean; message?: string } => {
  if (!phone) {
    return { isValid: false, message: '请输入手机号' };
  }
  
  if (!PHONE_REGEX.test(phone)) {
    return { isValid: false, message: '请输入正确的手机号格式' };
  }
  
  return { isValid: true };
};

/**
 * 验证身份证号
 * @param idCard 身份证号
 * @returns 验证结果
 */
export const validateIdCard = (idCard: string): { isValid: boolean; message?: string } => {
  if (!idCard) {
    return { isValid: false, message: '请输入身份证号' };
  }
  
  if (!ID_CARD_REGEX.test(idCard)) {
    return { isValid: false, message: '请输入正确的身份证号格式' };
  }
  
  // 验证校验位
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idCard[i]) * weights[i];
  }
  
  const checkCode = checkCodes[sum % 11];
  if (checkCode !== idCard[17].toUpperCase()) {
    return { isValid: false, message: '身份证号校验位错误' };
  }
  
  return { isValid: true };
};

/**
 * 验证密码强度
 * @param password 密码
 * @returns 验证结果
 */
export const validatePassword = (password: string): { isValid: boolean; message?: string; strength?: 'weak' | 'medium' | 'strong' } => {
  if (!password) {
    return { isValid: false, message: '请输入密码' };
  }
  
  if (password.length < 6) {
    return { isValid: false, message: '密码长度至少6位', strength: 'weak' };
  }
  
  if (!PASSWORD_REGEX.test(password)) {
    return { isValid: false, message: '密码必须包含字母和数字', strength: 'weak' };
  }
  
  // 判断密码强度
  let strength: 'weak' | 'medium' | 'strong' = 'medium';
  
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecial = /[@$!%*#?&]/.test(password);
  const isLongEnough = password.length >= 8;
  
  const strengthScore = [hasLower, hasUpper, hasNumber, hasSpecial, isLongEnough].filter(Boolean).length;
  
  if (strengthScore >= 4) {
    strength = 'strong';
  } else if (strengthScore >= 2) {
    strength = 'medium';
  } else {
    strength = 'weak';
  }
  
  return { isValid: true, strength };
};

/**
 * 验证验证码
 * @param code 验证码
 * @returns 验证结果
 */
export const validateVerificationCode = (code: string): { isValid: boolean; message?: string } => {
  if (!code) {
    return { isValid: false, message: '请输入验证码' };
  }
  
  if (!VERIFICATION_CODE_REGEX.test(code)) {
    return { isValid: false, message: '验证码格式错误，应为6位数字' };
  }
  
  return { isValid: true };
};

/**
 * 验证姓名
 * @param name 姓名
 * @returns 验证结果
 */
export const validateName = (name: string): { isValid: boolean; message?: string } => {
  if (!name) {
    return { isValid: false, message: '请输入姓名' };
  }
  
  if (name.trim().length < 2) {
    return { isValid: false, message: '姓名至少2个字符' };
  }
  
  if (name.trim().length > 20) {
    return { isValid: false, message: '姓名不能超过20个字符' };
  }
  
  // 检查是否包含特殊字符
  const specialChars = /[!@#$%^&*(),.?":{}|<>]/;
  if (specialChars.test(name)) {
    return { isValid: false, message: '姓名不能包含特殊字符' };
  }
  
  return { isValid: true };
};

/**
 * 验证房间号
 * @param room 房间号
 * @returns 验证结果
 */
export const validateRoom = (room: string): { isValid: boolean; message?: string } => {
  if (!room) {
    return { isValid: false, message: '请输入房间号' };
  }
  
  if (!ROOM_REGEX.test(room)) {
    return { isValid: false, message: '房间号格式错误，应为3-4位数字' };
  }
  
  return { isValid: true };
};

/**
 * 验证邮箱
 * @param email 邮箱
 * @returns 验证结果
 */
export const validateEmail = (email: string): { isValid: boolean; message?: string } => {
  if (!email) {
    return { isValid: false, message: '请输入邮箱' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: '请输入正确的邮箱格式' };
  }
  
  return { isValid: true };
};

/**
 * 验证必填字段
 * @param value 值
 * @param fieldName 字段名
 * @returns 验证结果
 */
export const validateRequired = (value: any, fieldName: string): { isValid: boolean; message?: string } => {
  if (value === null || value === undefined || value === '') {
    return { isValid: false, message: `请输入${fieldName}` };
  }
  
  if (typeof value === 'string' && value.trim() === '') {
    return { isValid: false, message: `请输入${fieldName}` };
  }
  
  return { isValid: true };
};

/**
 * 验证字符串长度
 * @param value 值
 * @param min 最小长度
 * @param max 最大长度
 * @param fieldName 字段名
 * @returns 验证结果
 */
export const validateLength = (
  value: string, 
  min: number, 
  max: number, 
  fieldName: string
): { isValid: boolean; message?: string } => {
  if (!value) {
    return { isValid: false, message: `请输入${fieldName}` };
  }
  
  if (value.length < min) {
    return { isValid: false, message: `${fieldName}至少${min}个字符` };
  }
  
  if (value.length > max) {
    return { isValid: false, message: `${fieldName}不能超过${max}个字符` };
  }
  
  return { isValid: true };
};
