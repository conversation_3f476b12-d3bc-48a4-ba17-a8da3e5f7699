/**
 * 设备相关工具函数
 */

import { Dimensions, Platform } from 'react-native';

// 获取设备信息
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * 检查是否为iOS平台
 * @returns 是否为iOS
 */
export const isIOS = (): boolean => {
  return Platform.OS === 'ios';
};

/**
 * 检查是否为Android平台
 * @returns 是否为Android
 */
export const isAndroid = (): boolean => {
  return Platform.OS === 'android';
};

/**
 * 检查是否为Web平台
 * @returns 是否为Web
 */
export const isWeb = (): boolean => {
  return Platform.OS === 'web';
};

/**
 * 获取屏幕宽度
 * @returns 屏幕宽度
 */
export const getScreenWidth = (): number => {
  return screenWidth;
};

/**
 * 获取屏幕高度
 * @returns 屏幕高度
 */
export const getScreenHeight = (): number => {
  return screenHeight;
};

/**
 * 检查是否为小屏设备（宽度小于375px）
 * @returns 是否为小屏设备
 */
export const isSmallScreen = (): boolean => {
  return screenWidth < 375;
};

/**
 * 检查是否为平板设备（宽度大于768px）
 * @returns 是否为平板设备
 */
export const isTablet = (): boolean => {
  return screenWidth >= 768;
};

/**
 * 根据屏幕宽度计算响应式值
 * @param small 小屏值
 * @param medium 中屏值
 * @param large 大屏值
 * @returns 响应式值
 */
export const getResponsiveValue = <T>(small: T, medium: T, large: T): T => {
  if (screenWidth < 375) {
    return small;
  } else if (screenWidth < 768) {
    return medium;
  } else {
    return large;
  }
};

/**
 * 根据屏幕宽度计算字体大小
 * @param baseSize 基础字体大小
 * @returns 响应式字体大小
 */
export const getResponsiveFontSize = (baseSize: number): number => {
  const scale = screenWidth / 375; // 以iPhone 6/7/8为基准
  const newSize = baseSize * scale;
  
  // 限制字体大小范围
  return Math.max(12, Math.min(newSize, baseSize * 1.2));
};

/**
 * 根据屏幕宽度计算间距
 * @param baseSpacing 基础间距
 * @returns 响应式间距
 */
export const getResponsiveSpacing = (baseSpacing: number): number => {
  return getResponsiveValue(
    baseSpacing * 0.8,  // 小屏
    baseSpacing,        // 中屏
    baseSpacing * 1.2   // 大屏
  );
};

/**
 * 获取安全区域内边距
 * @returns 安全区域内边距
 */
export const getSafeAreaInsets = () => {
  // 这里返回默认值，实际使用时应该结合react-native-safe-area-context
  return {
    top: isIOS() ? 44 : 24,
    bottom: isIOS() ? 34 : 0,
    left: 0,
    right: 0,
  };
};

/**
 * 检查设备是否支持生物识别
 * @returns 是否支持生物识别
 */
export const supportsBiometrics = (): boolean => {
  // 这里返回默认值，实际使用时应该结合相关库检测
  return !isWeb();
};

/**
 * 检查设备是否支持NFC
 * @returns 是否支持NFC
 */
export const supportsNFC = (): boolean => {
  // 这里返回默认值，实际使用时应该结合相关库检测
  return isAndroid();
};

/**
 * 检查设备是否支持相机
 * @returns 是否支持相机
 */
export const supportsCamera = (): boolean => {
  return !isWeb();
};

/**
 * 获取设备类型描述
 * @returns 设备类型描述
 */
export const getDeviceType = (): string => {
  if (isWeb()) {
    return 'Web';
  } else if (isTablet()) {
    return isIOS() ? 'iPad' : 'Android Tablet';
  } else {
    return isIOS() ? 'iPhone' : 'Android Phone';
  }
};

/**
 * 检查是否为开发环境
 * @returns 是否为开发环境
 */
export const isDevelopment = (): boolean => {
  return __DEV__;
};

/**
 * 生成设备唯一标识
 * @returns 设备唯一标识
 */
export const generateDeviceId = (): string => {
  // 这里生成一个简单的设备ID，实际使用时应该使用更可靠的方法
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2);
  return `${Platform.OS}_${timestamp}_${random}`;
};

/**
 * 获取平台特定的样式
 * @param iosStyle iOS样式
 * @param androidStyle Android样式
 * @param webStyle Web样式
 * @returns 平台特定样式
 */
export const getPlatformStyle = <T>(
  iosStyle: T,
  androidStyle: T,
  webStyle?: T
): T => {
  if (isIOS()) {
    return iosStyle;
  } else if (isAndroid()) {
    return androidStyle;
  } else if (isWeb() && webStyle) {
    return webStyle;
  } else {
    return androidStyle; // 默认使用Android样式
  }
};

/**
 * 获取状态栏高度
 * @returns 状态栏高度
 */
export const getStatusBarHeight = (): number => {
  return getSafeAreaInsets().top;
};

/**
 * 获取底部安全区域高度
 * @returns 底部安全区域高度
 */
export const getBottomSafeAreaHeight = (): number => {
  return getSafeAreaInsets().bottom;
};
