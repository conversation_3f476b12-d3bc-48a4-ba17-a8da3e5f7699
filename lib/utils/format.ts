/**
 * 格式化工具函数
 */

/**
 * 手机号脱敏处理
 * @param phone 手机号
 * @returns 脱敏后的手机号
 */
export const maskPhone = (phone: string): string => {
  if (!phone || phone.length !== 11) {
    return phone;
  }
  
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

/**
 * 身份证号脱敏处理
 * @param idCard 身份证号
 * @returns 脱敏后的身份证号
 */
export const maskIdCard = (idCard: string): string => {
  if (!idCard || idCard.length !== 18) {
    return idCard;
  }
  
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
};

/**
 * 姓名脱敏处理
 * @param name 姓名
 * @returns 脱敏后的姓名
 */
export const maskName = (name: string): string => {
  if (!name || name.length < 2) {
    return name;
  }
  
  if (name.length === 2) {
    return name[0] + '*';
  }
  
  return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1];
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * 格式化数字，添加千分位分隔符
 * @param num 数字
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * 格式化百分比
 * @param value 数值
 * @param total 总数
 * @param decimals 小数位数
 * @returns 百分比字符串
 */
export const formatPercentage = (value: number, total: number, decimals: number = 1): string => {
  if (total === 0) return '0%';
  
  const percentage = (value / total) * 100;
  return percentage.toFixed(decimals) + '%';
};

/**
 * 格式化房间号显示
 * @param building 楼栋
 * @param unit 单元
 * @param floor 楼层
 * @param room 房间号
 * @returns 格式化后的房间号
 */
export const formatRoomNumber = (building: string, unit: string, floor: string, room: string): string => {
  return `${building}${unit}${floor}${room}`;
};

/**
 * 格式化房间号简短显示
 * @param building 楼栋
 * @param unit 单元
 * @param floor 楼层
 * @param room 房间号
 * @returns 简短的房间号
 */
export const formatRoomNumberShort = (building: string, unit: string, floor: string, room: string): string => {
  // 提取数字部分
  const buildingNum = building.replace(/[^\d]/g, '');
  const unitNum = unit.replace(/[^\d]/g, '');
  const floorNum = floor.replace(/[^\d]/g, '');
  
  return `${buildingNum}-${unitNum}-${room}`;
};

/**
 * 格式化状态显示
 * @param status 状态值
 * @returns 状态显示文本
 */
export const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    // 住户状态
    verified: '已认证',
    pending: '待审核',
    rejected: '已拒绝',
    
    // 通知状态
    published: '已发布',
    withdrawn: '已撤回',
    
    // 门禁状态
    active: '正常',
    inactive: '禁用',
    expired: '已过期',
    
    // 通用状态
    enabled: '启用',
    disabled: '禁用',
    success: '成功',
    failed: '失败',
    processing: '处理中',
  };
  
  return statusMap[status] || status;
};

/**
 * 格式化关系显示
 * @param relation 关系值
 * @returns 关系显示文本
 */
export const formatRelation = (relation: string): string => {
  const relationMap: Record<string, string> = {
    owner: '业主',
    tenant: '租户',
    family: '家属',
    other: '其他',
  };
  
  return relationMap[relation] || relation;
};

/**
 * 格式化注册方式显示
 * @param method 注册方式
 * @returns 注册方式显示文本
 */
export const formatRegistrationMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    property: '物业登记',
    self: '自主注册',
  };
  
  return methodMap[method] || method;
};

/**
 * 格式化通知分类显示
 * @param category 分类值
 * @returns 分类显示文本
 */
export const formatNoticeCategory = (category: string): string => {
  const categoryMap: Record<string, string> = {
    property_notice: '物业通知',
    lost_found: '失物招领',
    seeking_help: '寻求帮助',
    idle_items: '闲置物品',
  };
  
  return categoryMap[category] || category;
};

/**
 * 格式化门禁类型显示
 * @param type 门禁类型
 * @returns 门禁类型显示文本
 */
export const formatAccessType = (type: string): string => {
  const typeMap: Record<string, string> = {
    password: '密码',
    face: '人脸',
    card: '门卡',
    qr_code: '二维码',
  };
  
  return typeMap[type] || type;
};

/**
 * 截断文本并添加省略号
 * @param text 文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength) + '...';
};

/**
 * 首字母大写
 * @param str 字符串
 * @returns 首字母大写的字符串
 */
export const capitalize = (str: string): string => {
  if (!str) return str;
  
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * 驼峰命名转换为短横线命名
 * @param str 驼峰命名字符串
 * @returns 短横线命名字符串
 */
export const camelToKebab = (str: string): string => {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
};

/**
 * 短横线命名转换为驼峰命名
 * @param str 短横线命名字符串
 * @returns 驼峰命名字符串
 */
export const kebabToCamel = (str: string): string => {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
};
