/**
 * 日期格式化工具
 */

import { format, parseISO, isValid, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 常用日期格式
export const DATE_FORMATS = {
  DATE: 'yyyy-MM-dd',
  TIME: 'HH:mm:ss',
  DATETIME: 'yyyy-MM-dd HH:mm:ss',
  DATETIME_SHORT: 'MM-dd HH:mm',
  MONTH_DAY: 'MM月dd日',
  YEAR_MONTH: 'yyyy年MM月',
  CHINESE_DATE: 'yyyy年MM月dd日',
  CHINESE_DATETIME: 'yyyy年MM月dd日 HH:mm',
} as const;

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param formatStr 格式字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, formatStr: string = DATE_FORMATS.DATETIME): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) {
      return '无效日期';
    }
    return format(dateObj, formatStr, { locale: zhCN });
  } catch (error) {
    console.error('Date formatting error:', error);
    return '格式化失败';
  }
};

/**
 * 格式化相对时间（多久前）
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: string | Date): string => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) {
      return '无效日期';
    }

    const now = new Date();
    const diffInMinutes = differenceInMinutes(now, dateObj);
    const diffInHours = differenceInHours(now, dateObj);
    const diffInDays = differenceInDays(now, dateObj);

    if (diffInMinutes < 1) {
      return '刚刚';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInDays < 7) {
      return `${diffInDays}天前`;
    } else {
      return formatDate(dateObj, DATE_FORMATS.MONTH_DAY);
    }
  } catch (error) {
    console.error('Relative time formatting error:', error);
    return '时间错误';
  }
};

/**
 * 获取今天的日期范围
 * @returns 今天的开始和结束时间
 */
export const getTodayRange = (): { start: string; end: string } => {
  const today = new Date();
  const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
  
  return {
    start: start.toISOString(),
    end: end.toISOString(),
  };
};

/**
 * 获取本周的日期范围
 * @returns 本周的开始和结束时间
 */
export const getThisWeekRange = (): { start: string; end: string } => {
  const today = new Date();
  const dayOfWeek = today.getDay();
  const start = new Date(today);
  start.setDate(today.getDate() - dayOfWeek);
  start.setHours(0, 0, 0, 0);
  
  const end = new Date(start);
  end.setDate(start.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  
  return {
    start: start.toISOString(),
    end: end.toISOString(),
  };
};

/**
 * 获取本月的日期范围
 * @returns 本月的开始和结束时间
 */
export const getThisMonthRange = (): { start: string; end: string } => {
  const today = new Date();
  const start = new Date(today.getFullYear(), today.getMonth(), 1);
  const end = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59);
  
  return {
    start: start.toISOString(),
    end: end.toISOString(),
  };
};

/**
 * 检查日期是否为今天
 * @param date 日期字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (date: string | Date): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) {
      return false;
    }
    
    const today = new Date();
    return (
      dateObj.getDate() === today.getDate() &&
      dateObj.getMonth() === today.getMonth() &&
      dateObj.getFullYear() === today.getFullYear()
    );
  } catch (error) {
    return false;
  }
};

/**
 * 检查日期是否为昨天
 * @param date 日期字符串或Date对象
 * @returns 是否为昨天
 */
export const isYesterday = (date: string | Date): boolean => {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) {
      return false;
    }
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return (
      dateObj.getDate() === yesterday.getDate() &&
      dateObj.getMonth() === yesterday.getMonth() &&
      dateObj.getFullYear() === yesterday.getFullYear()
    );
  } catch (error) {
    return false;
  }
};

/**
 * 获取友好的日期显示
 * @param date 日期字符串或Date对象
 * @returns 友好的日期字符串
 */
export const getFriendlyDate = (date: string | Date): string => {
  if (isToday(date)) {
    return `今天 ${formatDate(date, 'HH:mm')}`;
  } else if (isYesterday(date)) {
    return `昨天 ${formatDate(date, 'HH:mm')}`;
  } else {
    return formatDate(date, DATE_FORMATS.DATETIME_SHORT);
  }
};
