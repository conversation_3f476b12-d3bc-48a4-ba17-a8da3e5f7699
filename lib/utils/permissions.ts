/**
 * 权限检查工具
 */

import { User } from '@/types/user';

// 权限常量
export const PERMISSIONS = {
  // 住户管理权限
  RESIDENT_MANAGEMENT: 'resident_management',
  RESIDENT_CREATE: 'resident_create',
  RESIDENT_UPDATE: 'resident_update',
  RESIDENT_DELETE: 'resident_delete',
  RESIDENT_APPROVE: 'resident_approve',
  
  // 组织发展权限
  ORGANIZATION_DEVELOPMENT: 'organization_development',
  STAFF_CREATE: 'staff_create',
  STAFF_UPDATE: 'staff_update',
  STAFF_DELETE: 'staff_delete',
  POSITION_CREATE: 'position_create',
  POSITION_UPDATE: 'position_update',
  POSITION_DELETE: 'position_delete',
  
  // 门禁管理权限
  ACCESS_MANAGEMENT: 'access_management',
  ACCESS_SET_PASSWORD: 'access_set_password',
  ACCESS_RECORD_FACE: 'access_record_face',
  ACCESS_RECORD_CARD: 'access_record_card',
  ACCESS_DELETE_CREDENTIAL: 'access_delete_credential',
  
  // 通知公告权限
  NOTICE_MANAGEMENT: 'notice_management',
  NOTICE_CREATE: 'notice_create',
  NOTICE_UPDATE: 'notice_update',
  NOTICE_DELETE: 'notice_delete',
  NOTICE_WITHDRAW: 'notice_withdraw',
  
  // 电话管理权限
  PHONE_MANAGEMENT: 'phone_management',
  PHONE_CREATE: 'phone_create',
  PHONE_UPDATE: 'phone_update',
  PHONE_DELETE: 'phone_delete',
  
  // 统计查看权限
  STATISTICS: 'statistics',
  STATISTICS_RESIDENTS: 'statistics_residents',
  STATISTICS_ACCESS: 'statistics_access',
  
  // 门禁日志权限
  ACCESS_LOGS: 'access_logs',
  
  // 系统管理权限
  SYSTEM_MANAGEMENT: 'system_management',
  USER_MANAGEMENT: 'user_management',
} as const;

// 权限组
export const PERMISSION_GROUPS = {
  // 管家权限组（所有权限）
  HOUSEKEEPER: [
    PERMISSIONS.RESIDENT_MANAGEMENT,
    PERMISSIONS.ORGANIZATION_DEVELOPMENT,
    PERMISSIONS.ACCESS_MANAGEMENT,
    PERMISSIONS.NOTICE_MANAGEMENT,
    PERMISSIONS.PHONE_MANAGEMENT,
    PERMISSIONS.STATISTICS,
    PERMISSIONS.ACCESS_LOGS,
  ],
  
  // 保安权限组
  SECURITY: [
    PERMISSIONS.ACCESS_MANAGEMENT,
  ],
  
  // 保洁权限组
  CLEANER: [
    PERMISSIONS.ACCESS_MANAGEMENT,
  ],
  
  // 维修权限组
  MAINTENANCE: [
    PERMISSIONS.ACCESS_MANAGEMENT,
  ],
} as const;

/**
 * 检查用户是否有指定权限
 * @param user 用户信息
 * @param permission 权限标识
 * @returns 是否有权限
 */
export const hasPermission = (user: User | null, permission: string): boolean => {
  if (!user) {
    return false;
  }
  
  // 管理员拥有所有权限
  if (user.role === 'admin') {
    return true;
  }
  
  // 检查用户权限列表
  return user.permissions.includes(permission) || user.permissions.includes('all');
};

/**
 * 检查用户是否有任一权限
 * @param user 用户信息
 * @param permissions 权限列表
 * @returns 是否有任一权限
 */
export const hasAnyPermission = (user: User | null, permissions: string[]): boolean => {
  if (!user) {
    return false;
  }
  
  return permissions.some(permission => hasPermission(user, permission));
};

/**
 * 检查用户是否有所有权限
 * @param user 用户信息
 * @param permissions 权限列表
 * @returns 是否有所有权限
 */
export const hasAllPermissions = (user: User | null, permissions: string[]): boolean => {
  if (!user) {
    return false;
  }
  
  return permissions.every(permission => hasPermission(user, permission));
};

/**
 * 检查用户是否有楼栋权限
 * @param user 用户信息
 * @param buildingId 楼栋ID
 * @returns 是否有楼栋权限
 */
export const hasBuildingAccess = (user: User | null, buildingId: string): boolean => {
  if (!user) {
    return false;
  }
  
  // 管理员拥有所有楼栋权限
  if (user.role === 'admin') {
    return true;
  }
  
  // 检查楼栋权限列表
  return user.buildingRights.includes(buildingId) || user.buildingRights.includes('all');
};

/**
 * 获取用户可访问的楼栋列表
 * @param user 用户信息
 * @param allBuildings 所有楼栋列表
 * @returns 可访问的楼栋列表
 */
export const getAccessibleBuildings = (user: User | null, allBuildings: string[]): string[] => {
  if (!user) {
    return [];
  }
  
  // 管理员可访问所有楼栋
  if (user.role === 'admin' || user.buildingRights.includes('all')) {
    return allBuildings;
  }
  
  // 返回用户有权限的楼栋
  return allBuildings.filter(building => user.buildingRights.includes(building));
};

/**
 * 检查用户是否可以操作指定住户
 * @param user 用户信息
 * @param residentBuildingIds 住户所在楼栋ID列表
 * @returns 是否可以操作
 */
export const canOperateResident = (user: User | null, residentBuildingIds: string[]): boolean => {
  if (!user) {
    return false;
  }
  
  // 需要有住户管理权限
  if (!hasPermission(user, PERMISSIONS.RESIDENT_MANAGEMENT)) {
    return false;
  }
  
  // 检查是否有任一楼栋权限
  return residentBuildingIds.some(buildingId => hasBuildingAccess(user, buildingId));
};

/**
 * 检查用户是否可以操作指定员工
 * @param user 用户信息
 * @param targetUser 目标用户
 * @returns 是否可以操作
 */
export const canOperateStaff = (user: User | null, targetUser: User): boolean => {
  if (!user) {
    return false;
  }
  
  // 需要有组织发展权限
  if (!hasPermission(user, PERMISSIONS.ORGANIZATION_DEVELOPMENT)) {
    return false;
  }
  
  // 不能操作自己
  if (user.id === targetUser.id) {
    return false;
  }
  
  // 管理员可以操作所有员工
  if (user.role === 'admin') {
    return true;
  }
  
  // 员工不能操作管理员
  if (targetUser.role === 'admin') {
    return false;
  }
  
  return true;
};

/**
 * 获取权限描述
 * @param permission 权限标识
 * @returns 权限描述
 */
export const getPermissionDescription = (permission: string): string => {
  const descriptions: Record<string, string> = {
    [PERMISSIONS.RESIDENT_MANAGEMENT]: '住户管理',
    [PERMISSIONS.ORGANIZATION_DEVELOPMENT]: '组织发展',
    [PERMISSIONS.ACCESS_MANAGEMENT]: '门禁管理',
    [PERMISSIONS.NOTICE_MANAGEMENT]: '通知公告',
    [PERMISSIONS.PHONE_MANAGEMENT]: '电话管理',
    [PERMISSIONS.STATISTICS]: '统计查看',
    [PERMISSIONS.ACCESS_LOGS]: '门禁日志',
    [PERMISSIONS.SYSTEM_MANAGEMENT]: '系统管理',
  };
  
  return descriptions[permission] || permission;
};

/**
 * 获取用户权限描述列表
 * @param user 用户信息
 * @returns 权限描述列表
 */
export const getUserPermissionDescriptions = (user: User | null): string[] => {
  if (!user) {
    return [];
  }
  
  if (user.role === 'admin' || user.permissions.includes('all')) {
    return ['所有权限'];
  }
  
  return user.permissions.map(permission => getPermissionDescription(permission));
};
