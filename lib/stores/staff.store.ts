/**
 * 员工管理状态管理
 */

import { create } from 'zustand';
import { Staff, Position, StaffState, StaffFilter, BUILT_IN_POSITIONS } from '@/types/staff';

interface StaffStore extends StaffState {
  // Staff Actions
  fetchStaff: (filter?: StaffFilter) => Promise<void>;
  addStaff: (staff: Omit<Staff, 'id'>) => Promise<void>;
  updateStaff: (id: string, updates: Partial<Staff>) => Promise<void>;
  deleteStaff: (id: string) => Promise<void>;
  
  // Position Actions
  fetchPositions: () => Promise<void>;
  addPosition: (position: Omit<Position, 'id'>) => Promise<void>;
  updatePosition: (id: string, updates: Partial<Position>) => Promise<void>;
  deletePosition: (id: string) => Promise<void>;
  
  // UI Actions
  setSelectedStaff: (staff: Staff | null) => void;
  setSelectedPosition: (position: Position | null) => void;
  setFilter: (filter: Partial<StaffFilter>) => void;
  clearFilter: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useStaffStore = create<StaffStore>((set, get) => ({
  // Initial state
  staff: [],
  positions: BUILT_IN_POSITIONS,
  loading: false,
  error: null,
  selectedStaff: null,
  selectedPosition: null,
  filter: {},

  // Staff Actions
  fetchStaff: async (filter?: StaffFilter) => {
    set({ loading: true, error: null });
    
    try {
      // TODO: 替换为实际的API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockStaff: Staff[] = [
        {
          id: 'staff_001',
          name: '张保安',
          phone: '137****9999',
          positions: ['security'],
          registrationTime: '2024-01-10 08:00:00',
          accessCredentials: {
            password: true,
            face: true,
            cards: ['card_staff_001']
          },
          accessRights: ['building_1_entrance', 'building_2_entrance'],
          buildingRights: ['building_1', 'building_2']
        },
        {
          id: 'staff_002',
          name: '李保洁',
          phone: '136****8888',
          positions: ['cleaner'],
          registrationTime: '2024-01-12 09:30:00',
          accessCredentials: {
            password: true,
            face: false,
            cards: []
          },
          accessRights: ['building_1_all_floors'],
          buildingRights: ['building_1']
        }
      ];

      // 应用筛选
      let filteredStaff = mockStaff;
      if (filter?.searchText) {
        filteredStaff = filteredStaff.filter(s => 
          s.name.includes(filter.searchText!) || 
          s.phone.includes(filter.searchText!)
        );
      }
      if (filter?.positions && filter.positions.length > 0) {
        filteredStaff = filteredStaff.filter(s => 
          s.positions.some(p => filter.positions!.includes(p))
        );
      }

      set({
        staff: filteredStaff,
        loading: false,
      });
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取员工列表失败',
      });
    }
  },

  addStaff: async (staffData: Omit<Staff, 'id'>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newStaff: Staff = {
        id: `staff_${Date.now()}`,
        ...staffData,
        registrationTime: new Date().toISOString(),
      };
      
      set(state => ({
        staff: [...state.staff, newStaff],
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '添加员工失败',
      });
      throw error;
    }
  },

  updateStaff: async (id: string, updates: Partial<Staff>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        staff: state.staff.map(s => 
          s.id === id ? { ...s, ...updates } : s
        ),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '更新员工失败',
      });
      throw error;
    }
  },

  deleteStaff: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        staff: state.staff.filter(s => s.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '删除员工失败',
      });
      throw error;
    }
  },

  // Position Actions
  fetchPositions: async () => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 获取自定义岗位（实际应该从API获取）
      const customPositions: Position[] = [
        {
          id: 'custom_001',
          name: '客服',
          permissions: ['resident_management', 'notice_management'],
          isBuiltIn: false,
          description: '负责住户服务的客服岗位'
        }
      ];

      set({
        positions: [...BUILT_IN_POSITIONS, ...customPositions],
        loading: false,
      });
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取岗位列表失败',
      });
    }
  },

  addPosition: async (positionData: Omit<Position, 'id'>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newPosition: Position = {
        id: `position_${Date.now()}`,
        ...positionData,
        isBuiltIn: false,
      };
      
      set(state => ({
        positions: [...state.positions, newPosition],
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '添加岗位失败',
      });
      throw error;
    }
  },

  updatePosition: async (id: string, updates: Partial<Position>) => {
    set({ loading: true, error: null });
    
    try {
      // 检查是否为内置岗位
      const position = get().positions.find(p => p.id === id);
      if (position?.isBuiltIn) {
        throw new Error('内置岗位不能修改');
      }

      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        positions: state.positions.map(p => 
          p.id === id ? { ...p, ...updates } : p
        ),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '更新岗位失败',
      });
      throw error;
    }
  },

  deletePosition: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      // 检查是否为内置岗位
      const position = get().positions.find(p => p.id === id);
      if (position?.isBuiltIn) {
        throw new Error('内置岗位不能删除');
      }

      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        positions: state.positions.filter(p => p.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '删除岗位失败',
      });
      throw error;
    }
  },

  // UI Actions
  setSelectedStaff: (staff: Staff | null) => {
    set({ selectedStaff: staff });
  },

  setSelectedPosition: (position: Position | null) => {
    set({ selectedPosition: position });
  },

  setFilter: (filter: Partial<StaffFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...filter }
    }));
  },

  clearFilter: () => {
    set({ filter: {} });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));
