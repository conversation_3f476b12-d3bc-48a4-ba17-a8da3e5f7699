/**
 * 通知公告状态管理
 */

import { create } from 'zustand';
import { Notice, NoticeState, NoticeFilter, NOTICE_CATEGORIES } from '@/types/notice';

interface NoticeStore extends NoticeState {
  // Actions
  fetchNotices: (filter?: NoticeFilter) => Promise<void>;
  addNotice: (notice: Omit<Notice, 'id'>) => Promise<void>;
  updateNotice: (id: string, updates: Partial<Notice>) => Promise<void>;
  deleteNotice: (id: string) => Promise<void>;
  withdrawNotice: (id: string) => Promise<void>;
  
  // UI Actions
  setSelectedNotice: (notice: Notice | null) => void;
  setFilter: (filter: Partial<NoticeFilter>) => void;
  clearFilter: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useNoticeStore = create<NoticeStore>((set, get) => ({
  // Initial state
  notices: [],
  loading: false,
  error: null,
  selectedNotice: null,
  filter: {},
  categories: NOTICE_CATEGORIES,

  // Actions
  fetchNotices: async (filter?: NoticeFilter) => {
    set({ loading: true, error: null });
    
    try {
      // TODO: 替换为实际的API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockNotices: Notice[] = [
        {
          id: 'notice_001',
          title: '小区停水通知',
          content: '因市政管网维修，本小区将于明日8:00-18:00停水，请各位业主提前储水。',
          category: 'property_notice',
          publisher: '物业服务中心',
          publishTime: '2024-01-15 16:30:00',
          status: 'published',
          images: ['https://example.com/notice1.jpg'],
          allowComments: true,
          allowPrivateMessage: false,
          recipients: ['building_1', 'building_2'],
          commentCount: 5,
          comments: [
            {
              id: 'comment_001',
              userId: 'resident_001',
              userName: '李业主',
              content: '知道了，谢谢提醒',
              time: '2024-01-15 17:00:00'
            }
          ]
        },
        {
          id: 'notice_002',
          title: '寻找丢失的钥匙',
          content: '在小区花园附近丢失一串钥匙，有拾到的朋友请联系我，谢谢！',
          category: 'lost_found',
          publisher: '王住户',
          publishTime: '2024-01-16 10:15:00',
          status: 'published',
          allowComments: true,
          allowPrivateMessage: true,
          recipients: ['building_1'],
          commentCount: 2
        }
      ];

      // 应用筛选
      let filteredNotices = mockNotices;
      if (filter?.status) {
        filteredNotices = filteredNotices.filter(n => n.status === filter.status);
      }
      if (filter?.category) {
        filteredNotices = filteredNotices.filter(n => n.category === filter.category);
      }
      if (filter?.searchText) {
        filteredNotices = filteredNotices.filter(n => 
          n.title.includes(filter.searchText!) || 
          n.content.includes(filter.searchText!)
        );
      }

      set({
        notices: filteredNotices,
        loading: false,
      });
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取通知列表失败',
      });
    }
  },

  addNotice: async (noticeData: Omit<Notice, 'id'>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newNotice: Notice = {
        id: `notice_${Date.now()}`,
        ...noticeData,
        publishTime: new Date().toISOString(),
        status: 'published',
        commentCount: 0,
      };
      
      set(state => ({
        notices: [newNotice, ...state.notices],
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '发布通知失败',
      });
      throw error;
    }
  },

  updateNotice: async (id: string, updates: Partial<Notice>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        notices: state.notices.map(n => 
          n.id === id ? { ...n, ...updates } : n
        ),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '更新通知失败',
      });
      throw error;
    }
  },

  deleteNotice: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        notices: state.notices.filter(n => n.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '删除通知失败',
      });
      throw error;
    }
  },

  withdrawNotice: async (id: string) => {
    await get().updateNotice(id, { status: 'withdrawn' });
  },

  // UI Actions
  setSelectedNotice: (notice: Notice | null) => {
    set({ selectedNotice: notice });
  },

  setFilter: (filter: Partial<NoticeFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...filter }
    }));
  },

  clearFilter: () => {
    set({ filter: {} });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));
