/**
 * 住户管理状态管理
 */

import { create } from 'zustand';
import { Resident, ResidentState, ResidentFilter } from '@/types/resident';

interface ResidentStore extends ResidentState {
  // Actions
  fetchResidents: (filter?: ResidentFilter) => Promise<void>;
  addResident: (resident: Omit<Resident, 'id'>) => Promise<void>;
  updateResident: (id: string, updates: Partial<Resident>) => Promise<void>;
  deleteResident: (id: string) => Promise<void>;
  approveResident: (id: string) => Promise<void>;
  rejectResident: (id: string, reason: string) => Promise<void>;
  
  // UI Actions
  setSelectedResident: (resident: Resident | null) => void;
  setFilter: (filter: Partial<ResidentFilter>) => void;
  clearFilter: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Pagination
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
}

export const useResidentStore = create<ResidentStore>((set, get) => ({
  // Initial state
  residents: [],
  loading: false,
  error: null,
  selectedResident: null,
  filter: {},
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
  },

  // Actions
  fetchResidents: async (filter?: ResidentFilter) => {
    set({ loading: true, error: null });
    
    try {
      // TODO: 替换为实际的API调用
      // const response = await residentService.getResidents(filter);
      
      // Mock数据
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockResidents: Resident[] = [
        {
          id: 'resident_001',
          name: '李业主',
          phone: '139****1234',
          status: 'verified',
          rooms: [{
            building: '1号楼',
            unit: '1单元',
            floor: '3楼',
            room: '302',
            relation: 'owner'
          }],
          registrationMethod: 'property',
          registrationTime: '2024-01-15 10:30:00',
          verificationTime: '2024-01-15 14:20:00',
          accessCredentials: {
            password: true,
            face: true,
            cards: ['card_001', 'card_002']
          }
        },
        {
          id: 'resident_002',
          name: '王租户',
          phone: '138****5678',
          status: 'pending',
          rooms: [{
            building: '2号楼',
            unit: '1单元',
            floor: '5楼',
            room: '501',
            relation: 'tenant'
          }],
          registrationMethod: 'self',
          registrationTime: '2024-01-16 09:15:00',
          accessCredentials: {
            password: false,
            face: false,
            cards: []
          }
        }
      ];

      // 应用筛选
      let filteredResidents = mockResidents;
      if (filter?.status) {
        filteredResidents = filteredResidents.filter(r => r.status === filter.status);
      }
      if (filter?.searchText) {
        filteredResidents = filteredResidents.filter(r => 
          r.name.includes(filter.searchText!) || 
          r.phone.includes(filter.searchText!)
        );
      }

      set({
        residents: filteredResidents,
        loading: false,
        pagination: {
          ...get().pagination,
          total: filteredResidents.length,
        }
      });
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取住户列表失败',
      });
    }
  },

  addResident: async (residentData: Omit<Resident, 'id'>) => {
    set({ loading: true, error: null });
    
    try {
      // TODO: 替换为实际的API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newResident: Resident = {
        id: `resident_${Date.now()}`,
        ...residentData,
        registrationTime: new Date().toISOString(),
        status: 'verified'
      };
      
      set(state => ({
        residents: [...state.residents, newResident],
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '添加住户失败',
      });
      throw error;
    }
  },

  updateResident: async (id: string, updates: Partial<Resident>) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        residents: state.residents.map(r => 
          r.id === id ? { ...r, ...updates } : r
        ),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '更新住户失败',
      });
      throw error;
    }
  },

  deleteResident: async (id: string) => {
    set({ loading: true, error: null });
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set(state => ({
        residents: state.residents.filter(r => r.id !== id),
        loading: false,
      }));
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : '删除住户失败',
      });
      throw error;
    }
  },

  approveResident: async (id: string) => {
    await get().updateResident(id, { 
      status: 'verified',
      verificationTime: new Date().toISOString()
    });
  },

  rejectResident: async (id: string, reason: string) => {
    await get().updateResident(id, { 
      status: 'rejected',
      rejectReason: reason
    });
  },

  // UI Actions
  setSelectedResident: (resident: Resident | null) => {
    set({ selectedResident: resident });
  },

  setFilter: (filter: Partial<ResidentFilter>) => {
    set(state => ({
      filter: { ...state.filter, ...filter }
    }));
  },

  clearFilter: () => {
    set({ filter: {} });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  setPage: (page: number) => {
    set(state => ({
      pagination: { ...state.pagination, page }
    }));
  },

  setPageSize: (pageSize: number) => {
    set(state => ({
      pagination: { ...state.pagination, pageSize }
    }));
  },
}));
