/**
 * 通知公告相关类型定义
 */

export type NoticeCategory = 'property_notice' | 'lost_found' | 'seeking_help' | 'idle_items';
export type NoticeStatus = 'published' | 'withdrawn';

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  time: string;
  replies?: Comment[];
}

export interface Notice {
  id: string;
  title: string;
  content: string;
  category: NoticeCategory;
  publisher: string;
  publishTime: string;
  status: NoticeStatus;
  images?: string[];
  video?: string;
  allowComments: boolean;
  allowPrivateMessage: boolean;
  recipients: string[];
  commentCount: number;
  comments?: Comment[];
}

export interface NoticeFilter {
  status?: NoticeStatus;
  category?: NoticeCategory;
  dateRange?: {
    start: string;
    end: string;
  };
  searchText?: string;
}

export interface NoticeState {
  notices: Notice[];
  loading: boolean;
  error: string | null;
  selectedNotice: Notice | null;
  filter: NoticeFilter;
  categories: {
    id: NoticeCategory;
    name: string;
    description: string;
  }[];
}

// 通知分类常量
export const NOTICE_CATEGORIES = [
  {
    id: 'property_notice' as NoticeCategory,
    name: '物业通知',
    description: '物业管理相关的重要通知'
  },
  {
    id: 'lost_found' as NoticeCategory,
    name: '失物招领',
    description: '失物招领信息发布'
  },
  {
    id: 'seeking_help' as NoticeCategory,
    name: '寻求帮助',
    description: '住户互助信息发布'
  },
  {
    id: 'idle_items' as NoticeCategory,
    name: '闲置物品',
    description: '闲置物品交换信息'
  }
];
