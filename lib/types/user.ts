/**
 * 用户相关类型定义
 */

export interface User {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  nickname?: string;
  role: 'admin' | 'staff';
  positions: string[];
  permissions: string[];
  accessRights: string[];
  buildingRights: string[];
  community: {
    id: string;
    name: string;
  };
  registrationTime: string;
  lastLoginTime?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
}

export interface LoginCredentials {
  phone: string;
  verificationCode: string;
}

export interface BindAdminData {
  dynamicCode: string;
  password: string;
  name: string;
  phone: string;
  verificationCode: string;
}
