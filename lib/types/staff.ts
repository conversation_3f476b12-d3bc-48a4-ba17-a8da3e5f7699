/**
 * 员工相关类型定义
 */

export interface Position {
  id: string;
  name: string;
  permissions: string[];
  isBuiltIn: boolean;
  description?: string;
}

export interface Staff {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  positions: string[];
  registrationTime: string;
  accessCredentials: {
    password: boolean;
    face: boolean;
    cards: string[];
  };
  accessRights: string[];
  buildingRights: string[];
}

export interface StaffFilter {
  searchText?: string;
  positions?: string[];
  buildingRights?: string[];
}

export interface StaffState {
  staff: Staff[];
  positions: Position[];
  loading: boolean;
  error: string | null;
  selectedStaff: Staff | null;
  selectedPosition: Position | null;
  filter: StaffFilter;
}

// 内置岗位常量
export const BUILT_IN_POSITIONS: Position[] = [
  {
    id: 'housekeeper',
    name: '管家',
    permissions: [
      'resident_management',
      'organization_development', 
      'notice_management',
      'statistics',
      'access_logs',
      'access_management',
      'phone_management'
    ],
    isBuiltIn: true,
    description: '拥有所有功能权限的管理岗位'
  },
  {
    id: 'security',
    name: '保安',
    permissions: ['access_management'],
    isBuiltIn: true,
    description: '负责门禁管理的安保岗位'
  },
  {
    id: 'cleaner',
    name: '保洁',
    permissions: ['access_management'],
    isBuiltIn: true,
    description: '负责清洁工作的服务岗位'
  },
  {
    id: 'maintenance',
    name: '维修',
    permissions: ['access_management'],
    isBuiltIn: true,
    description: '负责设备维修的技术岗位'
  }
];
