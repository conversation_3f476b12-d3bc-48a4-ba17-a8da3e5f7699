/**
 * 住户相关类型定义
 */

export type ResidentStatus = 'verified' | 'pending' | 'rejected';
export type ResidentRelation = 'owner' | 'tenant';
export type RegistrationMethod = 'property' | 'self';

export interface Room {
  building: string;
  unit: string;
  floor: string;
  room: string;
  relation: ResidentRelation;
}

export interface AccessCredentials {
  password: boolean;
  face: boolean;
  cards: string[];
}

export interface Resident {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  status: ResidentStatus;
  rejectReason?: string;
  rooms: Room[];
  registrationMethod: RegistrationMethod;
  registrationTime: string;
  verificationTime?: string;
  accessCredentials: AccessCredentials;
}

export interface ResidentFilter {
  status?: ResidentStatus;
  building?: string;
  unit?: string;
  floor?: string;
  room?: string;
  searchText?: string;
}

export interface ResidentState {
  residents: Resident[];
  loading: boolean;
  error: string | null;
  selectedResident: Resident | null;
  filter: ResidentFilter;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}
