/**
 * API相关类型定义
 */

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 请求配置类型
export interface RequestConfig {
  timeout?: number;
  retries?: number;
  showLoading?: boolean;
  showError?: boolean;
}

// 错误响应类型
export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp: string;
}

// 上传文件响应类型
export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// 认证相关API类型
export interface LoginRequest {
  phone: string;
  verificationCode: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    name: string;
    phone: string;
    role: string;
    permissions: string[];
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

// 验证码相关API类型
export interface SendSmsRequest {
  phone: string;
  type: 'login' | 'bind' | 'reset';
}

export interface SendSmsResponse {
  success: boolean;
  message: string;
}

// 住户相关API类型
export interface ResidentListRequest {
  page?: number;
  pageSize?: number;
  status?: string;
  building?: string;
  unit?: string;
  floor?: string;
  room?: string;
  searchText?: string;
}

export interface CreateResidentRequest {
  name: string;
  phone: string;
  rooms: Array<{
    building: string;
    unit: string;
    floor: string;
    room: string;
    relation: 'owner' | 'tenant';
  }>;
  registrationMethod: 'property' | 'self';
}

// 员工相关API类型
export interface StaffListRequest {
  page?: number;
  pageSize?: number;
  searchText?: string;
  positions?: string[];
  buildingRights?: string[];
}

export interface CreateStaffRequest {
  name: string;
  phone: string;
  positions: string[];
  accessRights: string[];
  buildingRights: string[];
}

// 通知相关API类型
export interface NoticeListRequest {
  page?: number;
  pageSize?: number;
  status?: string;
  category?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  searchText?: string;
}

export interface CreateNoticeRequest {
  title: string;
  content: string;
  category: string;
  images?: string[];
  video?: string;
  allowComments: boolean;
  allowPrivateMessage: boolean;
  recipients: string[];
}

// 门禁相关API类型
export interface AccessLogRequest {
  page?: number;
  pageSize?: number;
  userId?: string;
  deviceId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  accessType?: 'password' | 'face' | 'card';
}

export interface SetPasswordRequest {
  userId: string;
  password: string;
}

export interface RecordFaceRequest {
  userId: string;
  faceData: string;
}

export interface RecordCardRequest {
  userId: string;
  cardId: string;
}
