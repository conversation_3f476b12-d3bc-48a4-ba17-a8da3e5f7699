/**
 * 导航类型定义
 */

import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

// 认证栈参数类型
export type AuthStackParamList = {
  Login: undefined;
  BindAdmin: undefined;
  BindSuccess: {
    adminName: string;
    adminPhone: string;
  };
};

// 主应用底部Tab参数类型
export type MainTabParamList = {
  Residents: undefined;
  Organization: undefined;
  Access: undefined;
  Statistics: undefined;
  Logs: undefined;
  Notices: undefined;
  Phones: undefined;
  Profile: undefined;
};

// 主应用栈参数类型
export type MainStackParamList = {
  // Tab导航
  MainTabs: undefined;
  
  // 住户管理子页面
  AddResident: undefined;
  EditResident: { id: string };
  ResidentDetail: { id: string };
  
  // 组织发展子页面
  AddStaff: undefined;
  EditStaff: { id: string };
  AddPosition: undefined;
  EditPosition: { id: string };
  
  // 门禁管理子页面
  SetPassword: { userId: string };
  RecordCard: { userId: string };
  RecordFace: { userId: string };
  Camera: { 
    userId: string;
    type: 'face' | 'card';
  };
  
  // 通知公告子页面
  PublishNotice: undefined;
  NoticeDetail: { id: string };
  
  // 电话管理子页面
  AddPhone: undefined;
  EditPhone: { id: string };
  
  // 设置子页面
  Settings: undefined;
};

// 根导航参数类型
export type RootStackParamList = {
  Auth: AuthStackParamList;
  Main: MainStackParamList;
};

// 屏幕Props类型定义
export type AuthScreenProps<T extends keyof AuthStackParamList> = 
  NativeStackScreenProps<AuthStackParamList, T>;

export type MainTabScreenProps<T extends keyof MainTabParamList> = 
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, T>,
    NativeStackScreenProps<MainStackParamList>
  >;

export type MainStackScreenProps<T extends keyof MainStackParamList> = 
  NativeStackScreenProps<MainStackParamList, T>;

// 导航图标类型
export type TabIconName = 
  | 'users'
  | 'organization'
  | 'key'
  | 'chart-bar'
  | 'history'
  | 'bell'
  | 'phone'
  | 'user';

// Tab配置类型
export interface TabConfig {
  name: keyof MainTabParamList;
  title: string;
  icon: TabIconName;
  component: React.ComponentType<any>;
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
