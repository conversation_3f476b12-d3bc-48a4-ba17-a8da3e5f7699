/**
 * 认证相关Mock数据服务
 */

import { LoginRequest, LoginResponse, SendSmsRequest, SendSmsResponse } from '@/types/api';
import { BindAdminData } from '@/types/user';

// Mock延迟
const mockDelay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Mock用户数据
const mockUsers = [
  {
    id: 'admin_001',
    name: '张管理员',
    phone: '13800138000',
    role: 'admin',
    permissions: ['all'],
    community: {
      id: 'community_001',
      name: '阳光花园小区'
    }
  },
  {
    id: 'staff_001',
    name: '李员工',
    phone: '13800138001',
    role: 'staff',
    permissions: ['resident_management', 'access_management'],
    community: {
      id: 'community_001',
      name: '阳光花园小区'
    }
  }
];

// 登录Mock服务
export const mockLogin = async (data: LoginRequest): Promise<LoginResponse> => {
  await mockDelay(1000);
  
  // 模拟验证码验证
  if (data.verificationCode !== '123456') {
    throw new Error('验证码错误');
  }
  
  // 查找用户
  const user = mockUsers.find(u => u.phone === data.phone);
  if (!user) {
    throw new Error('用户不存在');
  }
  
  return {
    token: `mock_token_${Date.now()}`,
    refreshToken: `mock_refresh_token_${Date.now()}`,
    user: {
      id: user.id,
      name: user.name,
      phone: user.phone,
      role: user.role,
      permissions: user.permissions,
    }
  };
};

// 发送短信Mock服务
export const mockSendSms = async (data: SendSmsRequest): Promise<SendSmsResponse> => {
  await mockDelay(500);
  
  // 模拟手机号验证
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(data.phone)) {
    throw new Error('手机号格式不正确');
  }
  
  console.log(`📱 Mock SMS sent to ${data.phone}, code: 123456`);
  
  return {
    success: true,
    message: '验证码发送成功'
  };
};

// 绑定管理员Mock服务
export const mockBindAdmin = async (data: BindAdminData): Promise<LoginResponse> => {
  await mockDelay(1500);
  
  // 模拟动态码验证
  if (data.dynamicCode !== 'ADMIN2024') {
    throw new Error('动态码错误');
  }
  
  // 模拟验证码验证
  if (data.verificationCode !== '123456') {
    throw new Error('验证码错误');
  }
  
  // 创建新管理员
  const newAdmin = {
    id: `admin_${Date.now()}`,
    name: data.name,
    phone: data.phone,
    role: 'admin' as const,
    permissions: ['all'],
  };
  
  return {
    token: `mock_token_${Date.now()}`,
    refreshToken: `mock_refresh_token_${Date.now()}`,
    user: newAdmin
  };
};

// 刷新Token Mock服务
export const mockRefreshToken = async (refreshToken: string) => {
  await mockDelay(300);
  
  if (!refreshToken || !refreshToken.startsWith('mock_refresh_token_')) {
    throw new Error('无效的刷新令牌');
  }
  
  return {
    token: `mock_token_${Date.now()}`,
    refreshToken: `mock_refresh_token_${Date.now()}`,
  };
};

// 登出Mock服务
export const mockLogout = async (): Promise<void> => {
  await mockDelay(200);
  console.log('🚪 User logged out');
};
