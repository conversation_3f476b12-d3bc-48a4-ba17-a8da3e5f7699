/**
 * 住户相关Mock数据服务
 */

import { Resident } from '@/types/resident';
import { ResidentListRequest, CreateResidentRequest, PaginatedResponse } from '@/types/api';

// Mock延迟
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Mock住户数据
let mockResidents: Resident[] = [
  {
    id: 'resident_001',
    name: '李业主',
    phone: '139****1234',
    status: 'verified',
    rooms: [{
      building: '1号楼',
      unit: '1单元',
      floor: '3楼',
      room: '302',
      relation: 'owner'
    }],
    registrationMethod: 'property',
    registrationTime: '2024-01-15 10:30:00',
    verificationTime: '2024-01-15 14:20:00',
    accessCredentials: {
      password: true,
      face: true,
      cards: ['card_001', 'card_002']
    }
  },
  {
    id: 'resident_002',
    name: '王租户',
    phone: '138****5678',
    status: 'pending',
    rooms: [{
      building: '2号楼',
      unit: '1单元',
      floor: '5楼',
      room: '501',
      relation: 'tenant'
    }],
    registrationMethod: 'self',
    registrationTime: '2024-01-16 09:15:00',
    accessCredentials: {
      password: false,
      face: false,
      cards: []
    }
  },
  {
    id: 'resident_003',
    name: '张住户',
    phone: '137****9999',
    status: 'rejected',
    rejectReason: '房屋信息不符',
    rooms: [{
      building: '1号楼',
      unit: '2单元',
      floor: '8楼',
      room: '801',
      relation: 'owner'
    }],
    registrationMethod: 'self',
    registrationTime: '2024-01-17 16:45:00',
    accessCredentials: {
      password: false,
      face: false,
      cards: []
    }
  }
];

// 获取住户列表Mock服务
export const mockGetResidents = async (params: ResidentListRequest): Promise<PaginatedResponse<Resident>> => {
  await mockDelay();
  
  let filteredResidents = [...mockResidents];
  
  // 应用筛选条件
  if (params.status) {
    filteredResidents = filteredResidents.filter(r => r.status === params.status);
  }
  
  if (params.building) {
    filteredResidents = filteredResidents.filter(r => 
      r.rooms.some(room => room.building === params.building)
    );
  }
  
  if (params.unit) {
    filteredResidents = filteredResidents.filter(r => 
      r.rooms.some(room => room.unit === params.unit)
    );
  }
  
  if (params.searchText) {
    filteredResidents = filteredResidents.filter(r => 
      r.name.includes(params.searchText!) || 
      r.phone.includes(params.searchText!)
    );
  }
  
  // 分页
  const page = params.page || 1;
  const pageSize = params.pageSize || 20;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const items = filteredResidents.slice(startIndex, endIndex);
  
  return {
    items,
    total: filteredResidents.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredResidents.length / pageSize)
  };
};

// 创建住户Mock服务
export const mockCreateResident = async (data: CreateResidentRequest): Promise<Resident> => {
  await mockDelay(800);
  
  // 验证手机号是否已存在
  const existingResident = mockResidents.find(r => r.phone === data.phone);
  if (existingResident) {
    throw new Error('该手机号已注册');
  }
  
  const newResident: Resident = {
    id: `resident_${Date.now()}`,
    name: data.name,
    phone: data.phone,
    status: data.registrationMethod === 'property' ? 'verified' : 'pending',
    rooms: data.rooms,
    registrationMethod: data.registrationMethod,
    registrationTime: new Date().toISOString(),
    verificationTime: data.registrationMethod === 'property' ? new Date().toISOString() : undefined,
    accessCredentials: {
      password: false,
      face: false,
      cards: []
    }
  };
  
  mockResidents.push(newResident);
  return newResident;
};

// 更新住户Mock服务
export const mockUpdateResident = async (id: string, updates: Partial<Resident>): Promise<Resident> => {
  await mockDelay(500);
  
  const index = mockResidents.findIndex(r => r.id === id);
  if (index === -1) {
    throw new Error('住户不存在');
  }
  
  mockResidents[index] = { ...mockResidents[index], ...updates };
  return mockResidents[index];
};

// 删除住户Mock服务
export const mockDeleteResident = async (id: string): Promise<void> => {
  await mockDelay(500);
  
  const index = mockResidents.findIndex(r => r.id === id);
  if (index === -1) {
    throw new Error('住户不存在');
  }
  
  mockResidents.splice(index, 1);
};

// 审核住户Mock服务
export const mockApproveResident = async (id: string): Promise<Resident> => {
  return mockUpdateResident(id, {
    status: 'verified',
    verificationTime: new Date().toISOString()
  });
};

// 拒绝住户Mock服务
export const mockRejectResident = async (id: string, reason: string): Promise<Resident> => {
  return mockUpdateResident(id, {
    status: 'rejected',
    rejectReason: reason
  });
};

// 获取住户详情Mock服务
export const mockGetResidentDetail = async (id: string): Promise<Resident> => {
  await mockDelay(300);
  
  const resident = mockResidents.find(r => r.id === id);
  if (!resident) {
    throw new Error('住户不存在');
  }
  
  return resident;
};
