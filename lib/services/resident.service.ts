/**
 * 住户服务
 */

import { request } from './api';
import { API_ENDPOINTS } from '@/constants/api';
import { ResidentListRequest, CreateResidentRequest, PaginatedResponse } from '@/types/api';
import { Resident } from '@/types/resident';

// 开发环境使用Mock服务
import { 
  mockGetResidents, 
  mockCreateResident, 
  mockUpdateResident, 
  mockDeleteResident,
  mockApproveResident,
  mockRejectResident,
  mockGetResidentDetail
} from './mock/resident.mock';

const USE_MOCK = __DEV__; // 开发环境使用Mock

export const residentService = {
  // 获取住户列表
  getResidents: async (params: ResidentListRequest): Promise<PaginatedResponse<Resident>> => {
    if (USE_MOCK) {
      return mockGetResidents(params);
    }
    return request.get<PaginatedResponse<Resident>>(API_ENDPOINTS.RESIDENTS.LIST, { params });
  },

  // 创建住户
  createResident: async (data: CreateResidentRequest): Promise<Resident> => {
    if (USE_MOCK) {
      return mockCreateResident(data);
    }
    return request.post<Resident>(API_ENDPOINTS.RESIDENTS.CREATE, data);
  },

  // 更新住户
  updateResident: async (id: string, data: Partial<Resident>): Promise<Resident> => {
    if (USE_MOCK) {
      return mockUpdateResident(id, data);
    }
    return request.put<Resident>(API_ENDPOINTS.RESIDENTS.UPDATE(id), data);
  },

  // 删除住户
  deleteResident: async (id: string): Promise<void> => {
    if (USE_MOCK) {
      return mockDeleteResident(id);
    }
    return request.delete<void>(API_ENDPOINTS.RESIDENTS.DELETE(id));
  },

  // 获取住户详情
  getResidentDetail: async (id: string): Promise<Resident> => {
    if (USE_MOCK) {
      return mockGetResidentDetail(id);
    }
    return request.get<Resident>(API_ENDPOINTS.RESIDENTS.DETAIL(id));
  },

  // 审核通过住户
  approveResident: async (id: string): Promise<Resident> => {
    if (USE_MOCK) {
      return mockApproveResident(id);
    }
    return request.post<Resident>(API_ENDPOINTS.RESIDENTS.APPROVE(id));
  },

  // 拒绝住户
  rejectResident: async (id: string, reason: string): Promise<Resident> => {
    if (USE_MOCK) {
      return mockRejectResident(id, reason);
    }
    return request.post<Resident>(API_ENDPOINTS.RESIDENTS.REJECT(id), { reason });
  },
};
