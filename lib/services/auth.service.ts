/**
 * 认证服务
 */

import { request } from './api';
import { API_ENDPOINTS } from '@/constants/api';
import { LoginRequest, LoginResponse, SendSmsRequest, SendSmsResponse, RefreshTokenRequest, RefreshTokenResponse } from '@/types/api';
import { BindAdminData } from '@/types/user';

// 开发环境使用Mock服务
import { mockLogin, mockSendSms, mockBindAdmin, mockRefreshToken, mockLogout } from './mock/auth.mock';

const USE_MOCK = __DEV__; // 开发环境使用Mock

export const authService = {
  // 登录
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    if (USE_MOCK) {
      return mockLogin(data);
    }
    return request.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, data);
  },

  // 发送短信验证码
  sendSms: async (data: SendSmsRequest): Promise<SendSmsResponse> => {
    if (USE_MOCK) {
      return mockSendSms(data);
    }
    return request.post<SendSmsResponse>(API_ENDPOINTS.AUTH.SEND_SMS, data);
  },

  // 绑定管理员
  bindAdmin: async (data: BindAdminData): Promise<LoginResponse> => {
    if (USE_MOCK) {
      return mockBindAdmin(data);
    }
    return request.post<LoginResponse>(API_ENDPOINTS.AUTH.BIND_ADMIN, data);
  },

  // 刷新Token
  refreshToken: async (data: RefreshTokenRequest): Promise<RefreshTokenResponse> => {
    if (USE_MOCK) {
      return mockRefreshToken(data.refreshToken);
    }
    return request.post<RefreshTokenResponse>(API_ENDPOINTS.AUTH.REFRESH_TOKEN, data);
  },

  // 登出
  logout: async (): Promise<void> => {
    if (USE_MOCK) {
      return mockLogout();
    }
    return request.post<void>(API_ENDPOINTS.AUTH.LOGOUT);
  },
};
