/**
 * API服务配置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_CONFIG, HTTP_STATUS, BUSINESS_ERROR_CODES, REQUEST_HEADERS, CONTENT_TYPES } from '@/constants/api';
import { ApiResponse, ApiError, RequestConfig } from '@/types/api';

// 创建Axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      [REQUEST_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
      [REQUEST_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = getAuthToken();
      if (token) {
        config.headers[REQUEST_HEADERS.AUTHORIZATION] = `Bearer ${token}`;
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId();

      // 打印请求日志（开发环境）
      if (__DEV__) {
        console.log('🚀 API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
          params: config.params,
        });
      }

      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 打印响应日志（开发环境）
      if (__DEV__) {
        console.log('✅ API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        });
      }

      // 检查业务状态码
      const { code, message, data } = response.data;
      if (code !== HTTP_STATUS.OK) {
        const error: ApiError = {
          code,
          message,
          timestamp: new Date().toISOString(),
        };
        return Promise.reject(error);
      }

      return response;
    },
    async (error) => {
      console.error('❌ Response Error:', error);

      // 网络错误
      if (!error.response) {
        const networkError: ApiError = {
          code: BUSINESS_ERROR_CODES.NETWORK_ERROR,
          message: '网络连接失败，请检查网络设置',
          timestamp: new Date().toISOString(),
        };
        return Promise.reject(networkError);
      }

      // HTTP错误
      const { status, data } = error.response;
      
      // Token过期，尝试刷新
      if (status === HTTP_STATUS.UNAUTHORIZED) {
        const refreshed = await tryRefreshToken();
        if (refreshed) {
          // 重新发送原请求
          return instance.request(error.config);
        } else {
          // 刷新失败，跳转到登录页
          handleAuthError();
        }
      }

      // 构造错误对象
      const apiError: ApiError = {
        code: data?.code || status,
        message: data?.message || getHttpErrorMessage(status),
        details: data?.details,
        timestamp: new Date().toISOString(),
      };

      return Promise.reject(apiError);
    }
  );

  return instance;
};

// 获取认证token
const getAuthToken = (): string | null => {
  // TODO: 从状态管理或存储中获取token
  // 这里暂时返回null，后续集成状态管理时实现
  return null;
};

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 尝试刷新token
const tryRefreshToken = async (): Promise<boolean> => {
  try {
    // TODO: 实现token刷新逻辑
    // const refreshToken = getRefreshToken();
    // const response = await refreshTokenApi(refreshToken);
    // setAuthToken(response.data.token);
    return false; // 暂时返回false
  } catch (error) {
    console.error('Token refresh failed:', error);
    return false;
  }
};

// 处理认证错误
const handleAuthError = (): void => {
  // TODO: 清除认证信息并跳转到登录页
  console.log('Authentication failed, redirecting to login...');
};

// 获取HTTP错误消息
const getHttpErrorMessage = (status: number): string => {
  const messages: Record<number, string> = {
    [HTTP_STATUS.BAD_REQUEST]: '请求参数错误',
    [HTTP_STATUS.UNAUTHORIZED]: '未授权访问',
    [HTTP_STATUS.FORBIDDEN]: '访问被禁止',
    [HTTP_STATUS.NOT_FOUND]: '请求的资源不存在',
    [HTTP_STATUS.INTERNAL_SERVER_ERROR]: '服务器内部错误',
    [HTTP_STATUS.BAD_GATEWAY]: '网关错误',
    [HTTP_STATUS.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  };
  
  return messages[status] || `HTTP错误: ${status}`;
};

// 创建API实例
export const apiInstance = createApiInstance();

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: RequestConfig) => 
    apiInstance.get<ApiResponse<T>>(url, config).then(res => res.data.data),
    
  post: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    apiInstance.post<ApiResponse<T>>(url, data, config).then(res => res.data.data),
    
  put: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    apiInstance.put<ApiResponse<T>>(url, data, config).then(res => res.data.data),
    
  delete: <T = any>(url: string, config?: RequestConfig) => 
    apiInstance.delete<ApiResponse<T>>(url, config).then(res => res.data.data),
    
  patch: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    apiInstance.patch<ApiResponse<T>>(url, data, config).then(res => res.data.data),
};

export default apiInstance;
