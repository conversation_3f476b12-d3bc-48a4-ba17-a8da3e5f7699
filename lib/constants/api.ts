/**
 * API常量配置
 */

// API基础配置
export const API_CONFIG = {
  BASE_URL: 'https://api.youjiapm.com',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
};

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH_TOKEN: '/auth/refresh',
    LOGOUT: '/auth/logout',
    SEND_SMS: '/auth/sms',
    BIND_ADMIN: '/auth/bind-admin',
  },
  
  // 住户管理
  RESIDENTS: {
    LIST: '/residents',
    CREATE: '/residents',
    UPDATE: (id: string) => `/residents/${id}`,
    DELETE: (id: string) => `/residents/${id}`,
    DETAIL: (id: string) => `/residents/${id}`,
    APPROVE: (id: string) => `/residents/${id}/approve`,
    REJECT: (id: string) => `/residents/${id}/reject`,
  },
  
  // 员工管理
  STAFF: {
    LIST: '/staff',
    CREATE: '/staff',
    UPDATE: (id: string) => `/staff/${id}`,
    DELETE: (id: string) => `/staff/${id}`,
    DETAIL: (id: string) => `/staff/${id}`,
  },
  
  // 岗位管理
  POSITIONS: {
    LIST: '/positions',
    CREATE: '/positions',
    UPDATE: (id: string) => `/positions/${id}`,
    DELETE: (id: string) => `/positions/${id}`,
  },
  
  // 通知公告
  NOTICES: {
    LIST: '/notices',
    CREATE: '/notices',
    UPDATE: (id: string) => `/notices/${id}`,
    DELETE: (id: string) => `/notices/${id}`,
    DETAIL: (id: string) => `/notices/${id}`,
    WITHDRAW: (id: string) => `/notices/${id}/withdraw`,
    COMMENTS: (id: string) => `/notices/${id}/comments`,
  },
  
  // 门禁管理
  ACCESS: {
    LOGS: '/access/logs',
    SET_PASSWORD: '/access/password',
    RECORD_FACE: '/access/face',
    RECORD_CARD: '/access/card',
    DELETE_CREDENTIAL: '/access/credential',
  },
  
  // 电话管理
  PHONES: {
    LIST: '/phones',
    CREATE: '/phones',
    UPDATE: (id: string) => `/phones/${id}`,
    DELETE: (id: string) => `/phones/${id}`,
  },
  
  // 统计数据
  STATISTICS: {
    OVERVIEW: '/statistics/overview',
    RESIDENTS: '/statistics/residents',
    ACCESS: '/statistics/access',
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: '/upload/image',
    VIDEO: '/upload/video',
    AVATAR: '/upload/avatar',
  },
};

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// 业务错误码
export const BUSINESS_ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 1000,
  INVALID_PARAMS: 1001,
  NETWORK_ERROR: 1002,
  
  // 认证错误
  INVALID_TOKEN: 2001,
  TOKEN_EXPIRED: 2002,
  INVALID_CREDENTIALS: 2003,
  SMS_SEND_FAILED: 2004,
  VERIFICATION_CODE_EXPIRED: 2005,
  
  // 权限错误
  PERMISSION_DENIED: 3001,
  ACCESS_DENIED: 3002,
  
  // 业务错误
  USER_NOT_FOUND: 4001,
  RESIDENT_NOT_FOUND: 4002,
  STAFF_NOT_FOUND: 4003,
  NOTICE_NOT_FOUND: 4004,
  PHONE_EXISTS: 4005,
  CARD_EXISTS: 4006,
} as const;

// 请求头
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
} as const;

// 内容类型
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
} as const;
