/**
 * 导航常量配置
 */

import { TabConfig } from '@/types/navigation';

// Tab导航配置
export const TAB_CONFIGS: TabConfig[] = [
  {
    name: 'Residents',
    title: '住户管理',
    icon: 'users',
    component: () => null, // 临时占位，后续替换为实际组件
  },
  {
    name: 'Organization',
    title: '组织发展',
    icon: 'organization',
    component: () => null,
  },
  {
    name: 'Access',
    title: '门禁管理',
    icon: 'key',
    component: () => null,
  },
  {
    name: 'Statistics',
    title: '登记统计',
    icon: 'chart-bar',
    component: () => null,
  },
  {
    name: 'Logs',
    title: '门禁日志',
    icon: 'history',
    component: () => null,
  },
  {
    name: 'Notices',
    title: '通知公告',
    icon: 'bell',
    component: () => null,
  },
  {
    name: 'Phones',
    title: '电话管理',
    icon: 'phone',
    component: () => null,
  },
  {
    name: 'Profile',
    title: '个人中心',
    icon: 'user',
    component: () => null,
  },
];

// 导航主题配置
export const NAVIGATION_THEME = {
  colors: {
    primary: '#007AFF',
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: '#000000',
    border: '#E5E5E7',
    notification: '#FF3B30',
  },
};

// Tab栏配置
export const TAB_BAR_CONFIG = {
  activeTintColor: '#007AFF',
  inactiveTintColor: '#8E8E93',
  style: {
    backgroundColor: '#FFFFFF',
    borderTopColor: '#E5E5E7',
    borderTopWidth: 1,
    height: 60,
    paddingBottom: 8,
    paddingTop: 8,
  },
  labelStyle: {
    fontSize: 12,
    fontWeight: '500' as const,
  },
  iconStyle: {
    marginBottom: 4,
  },
};
