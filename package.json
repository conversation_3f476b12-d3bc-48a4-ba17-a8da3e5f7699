{"name": "<PERSON><PERSON>a", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "setup": "node ./scripts/setup-project.js", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "lint:fix": "expo lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "progress": "node ./scripts/progress-tracker.js", "progress:report": "node ./scripts/progress-tracker.js report", "progress:next": "node ./scripts/progress-tracker.js next", "todo": "node ./scripts/todo-manager.js", "todo:stats": "node ./scripts/todo-manager.js stats", "todo:update": "node ./scripts/todo-manager.js update", "start:clean": "node scripts/start-clean.js", "clean": "rm -rf .expo .metro-cache tmp node_modules/.cache"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.79.0", "axios": "^1.6.0", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-camera": "~16.1.7", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.9.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zustand": "^4.5.7", "zustand-persist": "^0.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.8.4", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-expo": "~50.0.1", "lint-staged": "^15.0.0", "metro-cache": "^0.82.4", "prettier": "^3.0.0", "react-native-testing-library": "^2.2.0", "typescript": "~5.8.3", "uuid": "^11.1.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "private": true}